/* 弹出页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  width: 300px;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  padding: 16px;
}

h1 {
  font-size: 18px;
  margin-bottom: 16px;
  color: #1a73e8;
  text-align: center;
}

.format-section {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  outline: none;
}

select:focus {
  border-color: #1a73e8;
}

.buttons-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
}

.btn {
  padding: 10px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn.primary {
  background-color: #1a73e8;
  color: white;
}

.btn.primary:hover {
  background-color: #1669d3;
}

.btn.secondary {
  background-color: #f1f3f4;
  color: #1a73e8;
  border: 1px solid #1a73e8;
}

.btn.secondary:hover {
  background-color: #e8eaed;
}

.btn.info {
  background-color: #34a853;
  color: white;
  margin-top: 4px;
}

.btn.info:hover {
  background-color: #2d9249;
}

.auto-copy-section {
  margin-bottom: 16px;
}

.toggle-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #1a73e8;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.footer {
  text-align: center;
  margin-top: 8px;
}

.footer a {
  color: #1a73e8;
  text-decoration: none;
  font-size: 13px;
}

.footer a:hover {
  text-decoration: underline;
}
