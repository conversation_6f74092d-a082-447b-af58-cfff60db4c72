/**
 * 工具函数 - 提供格式化标签页信息的功能
 */

// 获取国际化消息的辅助函数
function getMessage(key) {
  // 使用 Chrome 的 i18n API 获取消息
  // 注意：Chrome 的 i18n API 会根据扩展的 manifest.json 中的 default_locale 设置
  // 和用户浏览器的语言设置来选择合适的语言文件
  const message = chrome.i18n.getMessage(key);

  // 如果消息为空或未定义，返回消息键
  return message || key;
}

/**
 * 根据指定格式格式化标签页信息
 * @param {object} tab - 标签页对象
 * @param {string} format - 格式类型
 * @returns {string} 格式化后的文本
 */
export function formatTabInfo(tab, format) {
  // 确保标题和URL不包含换行符
  const title = tab.title ? tab.title.replace(/\r?\n/g, ' ').trim() : '';
  const url = tab.url ? tab.url.replace(/\r?\n/g, '').trim() : '';

  switch (format) {
    case 'default': // [标题] URL
      return `[${title}]\r\n${url}`;

    case 'title-only': // [标题]
      return `[${title}]`;

    case 'url-only': // URL
      return url;

    case 'title-dash-url': // 标题 - URL
      return `${title} - ${url}`;

    case 'markdown': // [标题](URL)
      return `[${title}](${url})`;

    case 'html': // <a href="URL">标题</a>
      return `<a href="${url}">${title}</a>`;

    case 'csv': // "标题","URL"
      return `"${title.replace(/"/g, '""')}","${url}"`;

    case 'json': // JSON格式
      return JSON.stringify({ title, url }, null, 2);

    case 'html-table': // 单个标签页的HTML表格
      // 为单个标签页创建完整的HTML表格，添加样式
      return `<table style="border-collapse: collapse; width: 100%; max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif;">
  <thead>
    <tr style="background-color: #f2f2f2;">
      <th style="border: 1px solid #dddddd; text-align: left; padding: 8px;">${getMessage('title_column')}</th>
      <th style="border: 1px solid #dddddd; text-align: left; padding: 8px;">URL</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #dddddd; text-align: left; padding: 8px;">${title}</td>
      <td style="border: 1px solid #dddddd; text-align: left; padding: 8px;"><a href="${url}" style="color: #1a73e8; text-decoration: none;">${url}</a></td>
    </tr>
  </tbody>
</table>`;

    default:
      return `[${title}]\r\n${url}`;
  }
}

/**
 * 转义HTML特殊字符
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * 获取所有支持的复制格式
 * @returns {Array} 格式选项数组
 */
export function getSupportedFormats() {
  return [
    { id: 'default', name: chrome.i18n.getMessage('format_default'), description: chrome.i18n.getMessage('format_default_desc') },
    { id: 'title-only', name: chrome.i18n.getMessage('format_title_only'), description: chrome.i18n.getMessage('format_title_only_desc') },
    { id: 'url-only', name: chrome.i18n.getMessage('format_url_only'), description: chrome.i18n.getMessage('format_url_only_desc') },
    { id: 'title-dash-url', name: chrome.i18n.getMessage('format_title_dash_url'), description: chrome.i18n.getMessage('format_title_dash_url_desc') },
    { id: 'markdown', name: chrome.i18n.getMessage('format_markdown'), description: chrome.i18n.getMessage('format_markdown_desc') },
    { id: 'html', name: chrome.i18n.getMessage('format_html'), description: chrome.i18n.getMessage('format_html_desc') },
    { id: 'csv', name: chrome.i18n.getMessage('format_csv'), description: chrome.i18n.getMessage('format_csv_desc') },
    { id: 'json', name: chrome.i18n.getMessage('format_json'), description: chrome.i18n.getMessage('format_json_desc') },
    { id: 'html-table', name: chrome.i18n.getMessage('format_html_table'), description: chrome.i18n.getMessage('format_html_table_desc') }
  ];
}

/**
 * 格式化多个标签页为HTML表格
 * @param {Array} tabs - 标签页数组
 * @returns {string} HTML表格代码
 */
export function formatTabsAsHtmlTable(tabs) {
  if (!tabs || tabs.length === 0) {
    return '';
  }

  // 构建表格行
  const rows = tabs.map(tab => {
    const title = tab.title ? tab.title.trim() : '';
    const url = tab.url ? tab.url.trim() : '';
    return `    <tr>
      <td style="border: 1px solid #dddddd; text-align: left; padding: 8px;">${title}</td>
      <td style="border: 1px solid #dddddd; text-align: left; padding: 8px;"><a href="${url}" style="color: #1a73e8; text-decoration: none;">${url}</a></td>
    </tr>`;
  }).join('\n');

  // 返回完整的HTML表格
  return `<table style="border-collapse: collapse; width: 100%; max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif;">
  <thead>
    <tr style="background-color: #f2f2f2;">
      <th style="border: 1px solid #dddddd; text-align: left; padding: 8px;">${chrome.i18n.getMessage('title_column')}</th>
      <th style="border: 1px solid #dddddd; text-align: left; padding: 8px;">URL</th>
    </tr>
  </thead>
  <tbody>
${rows}
  </tbody>
</table>`;
}

/**
 * 格式化多个标签页为JSON
 * @param {Array} tabs - 标签页数组
 * @returns {string} JSON字符串
 */
export function formatTabsAsJson(tabs) {
  if (!tabs || tabs.length === 0) {
    return '[]';
  }

  const tabsData = tabs.map(tab => ({
    title: tab.title ? tab.title.trim() : '',
    url: tab.url ? tab.url.trim() : ''
  }));

  return JSON.stringify(tabsData, null, 2);
}
