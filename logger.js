/**
 * 日志控制模块
 * 默认禁用所有日志输出
 */

// 创建空的日志对象
window.logger = {
  debug: function() {},
  info: function() {},
  warn: function() {},
  error: function() {}
};

// 默认禁用所有日志输出
// 在正式环境中，不会显示任何日志

/**
 * 开发者指南：
 * 
 * 如果需要在开发过程中查看日志，请按照以下步骤操作：
 * 
 * 1. 打开浏览器控制台
 * 2. 在控制台中输入以下代码来启用日志：
 *    ```
 *    console._log = console.log;
 *    console._info = console.info;
 *    console._warn = console.warn;
 *    console._error = console.error;
 *    console._debug = console.debug;
 *    ```
 * 
 * 3. 如果需要再次禁用日志，请输入：
 *    ```
 *    console.log = function() {};
 *    console.info = function() {};
 *    console.warn = function() {};
 *    console.error = function() {};
 *    console.debug = function() {};
 *    ```
 */

// 禁用所有控制台日志
console.log = function() {};
console.info = function() {};
console.warn = function() {};
console.error = function() {};
console.debug = function() {};


// 启用日志
//console._log = console.log;
//console._info = console.info;
//console._warn = console.warn;
//console._error = console.error;
//console._debug = console.debug;