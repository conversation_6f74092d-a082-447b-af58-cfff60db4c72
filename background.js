// 背景脚本 - 处理标签页复制和快捷键监听
import { formatTabInfo, getSupportedFormats, formatTabsAsHtmlTable, formatTabsAsJson } from './utils.js';
import { clipboardHistory } from './clipboard-history.js';

// 默认设置
const DEFAULT_SETTINGS = {
  copyFormat: 'default', // [标题] URL
  autoTextCopy: true,
  minCharacters: 5,
  language: 'auto' // 自动检测语言
};

// 获取浏览器语言
function getBrowserLanguage() {
  // 获取浏览器语言
  const language = chrome.i18n.getUILanguage();
  console.log('Browser language detected (chrome.i18n.getUILanguage):', language);

  // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
  const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
  console.log('Raw browser language (navigator.language):', rawNavigatorLanguage);

  // 每次检测浏览器语言时，刷新上下文菜单以确保国际化正确应用
  refreshContextMenus();

  // 根据浏览器语言返回支持的语言代码
  if (language.startsWith('zh') || rawNavigatorLanguage.startsWith('zh')) {
    if (language.includes('TW') || language.includes('HK') || language.includes('MO') ||
        rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
      console.log('Using Traditional Chinese (zh_TW)');
      return 'zh_TW'; // 繁体中文
    } else {
      console.log('Using Simplified Chinese (zh_CN)');
      return 'zh_CN'; // 简体中文
    }
  } else {
    // 对于所有非中文语言，强制返回英语
    console.log('Using English (en) for non-Chinese language. Chrome API:', language, 'Navigator:', rawNavigatorLanguage);
    return 'en'; // 默认英语
  }
}

// 检查浏览器语言并通知所有页面
function checkBrowserLanguageAndNotify() {
  // 获取当前浏览器语言
  const browserLanguage = chrome.i18n.getUILanguage();
  console.log('检查浏览器语言:', browserLanguage);

  // 获取当前语言设置
  chrome.storage.sync.get(['language'], (result) => {
    const storedLanguage = result.language || 'auto';

    // 如果设置为自动，则通知所有页面检查浏览器语言
    if (storedLanguage === 'auto') {
      console.log('语言设置为自动，通知所有页面检查浏览器语言');

      // 通知所有页面检查浏览器语言
      chrome.runtime.sendMessage({
        action: 'checkBrowserLanguage'
      }).catch(() => {
        // 忽略错误，可能是没有打开的页面
      });

      // 通知所有标签页检查浏览器语言
      chrome.tabs.query({}).then(tabs => {
        for (const tab of tabs) {
          try {
            chrome.tabs.sendMessage(tab.id, {
              action: 'checkBrowserLanguage'
            }).catch(() => {
              // 忽略错误，可能是内容脚本还没有加载或者标签页不支持
            });
          } catch (e) {
            // 忽略可能的错误
          }
        }
      }).catch(error => {
        console.error('通知标签页检查浏览器语言失败:', error);
      });
    }
  });
}

// 刷新上下文菜单，确保国际化正确应用
function refreshContextMenus() {
  console.log('刷新上下文菜单以确保国际化正确应用');

  // 使用Chrome的国际化API获取菜单文本
  console.log('使用Chrome国际化API获取菜单文本');

  // 使用Chrome的国际化API获取菜单文本
  const extensionName = chrome.i18n.getMessage("extension_name");
  const copyCurrentTab = chrome.i18n.getMessage("copy_current_tab");
  const copyAllTabs = chrome.i18n.getMessage("copy_all_tabs");
  const clipboardHistory = chrome.i18n.getMessage("clipboard_history");

  console.log('从国际化API获取的菜单文本:');
  console.log('- 扩展名称:', extensionName);
  console.log('- 复制当前标签页:', copyCurrentTab);
  console.log('- 复制所有标签页:', copyAllTabs);
  console.log('- 剪贴板历史记录:', clipboardHistory);

  console.log('使用以下菜单文本:');
  console.log('- 扩展名称:', extensionName);
  console.log('- 复制当前标签页:', copyCurrentTab);
  console.log('- 复制所有标签页:', copyAllTabs);
  console.log('- 剪贴板历史记录:', clipboardHistory);

  // 移除所有现有菜单并重新创建
  chrome.contextMenus.removeAll(() => {
    // 创建主菜单项
    chrome.contextMenus.create({
      id: 'smartcopy-parent',
      title: extensionName,
      contexts: ['page', 'selection']
    });

    // 创建子菜单项
    chrome.contextMenus.create({
      id: 'copy-current-tab-parent',
      title: copyCurrentTab,
      parentId: 'smartcopy-parent',
      contexts: ['page']
    });

    chrome.contextMenus.create({
      id: 'copy-all-tabs-parent',
      title: copyAllTabs,
      parentId: 'smartcopy-parent',
      contexts: ['page']
    });

    chrome.contextMenus.create({
      id: 'view-clipboard-history',
      title: clipboardHistory,
      parentId: 'smartcopy-parent',
      contexts: ['page']
    });

    // 添加各种格式的子菜单
    const formats = getSupportedFormats();
    formats.forEach(format => {
      // 为复制当前标签页添加子菜单
      chrome.contextMenus.create({
        id: `copy-current-tab-${format.id}`,
        parentId: 'copy-current-tab-parent',
        title: format.name,
        contexts: ['page']
      });

      // 为复制所有标签页添加子菜单
      chrome.contextMenus.create({
        id: `copy-all-tabs-${format.id}`,
        parentId: 'copy-all-tabs-parent',
        title: format.name,
        contexts: ['page']
      });
    });
  });
}

// 定期检查浏览器语言
function startBrowserLanguageMonitor() {
  // 每5分钟检查一次浏览器语言
  setInterval(checkBrowserLanguageAndNotify, 300000); // 5分钟
}

// 初始化扩展
chrome.runtime.onInstalled.addListener(async () => {
  // 设置默认选项
  const settings = await chrome.storage.sync.get(DEFAULT_SETTINGS);
  if (Object.keys(settings).length === 0) {
    await chrome.storage.sync.set(DEFAULT_SETTINGS);
  }

  // 检查语言设置
  if (!settings.language) {
    // 只有在没有设置语言时才设置默认值
    await chrome.storage.sync.set({ language: 'auto' });
    console.log('Setting default language to auto');

    // 通知所有标签页更新语言设置
    try {
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { language: 'auto' }
      });
    } catch (error) {
      console.log('Failed to broadcast language update:', error);
    }
  } else {
    console.log('Using existing language setting:', settings.language);
  }

  // 启动浏览器语言监控
  startBrowserLanguageMonitor();

  // 立即检查一次浏览器语言
  checkBrowserLanguageAndNotify();

  // 在扩展启动时刷新上下文菜单，确保国际化正确应用
  console.log('扩展启动，刷新上下文菜单');
  refreshContextMenus();
});

// 处理上下文菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  // 处理复制当前标签页的各种格式
  if (info.menuItemId.startsWith('copy-current-tab-')) {
    const format = info.menuItemId.replace('copy-current-tab-', '');
    copySingleTab(tab, format);
  }
  // 处理复制所有标签页的各种格式
  else if (info.menuItemId.startsWith('copy-all-tabs-')) {
    const format = info.menuItemId.replace('copy-all-tabs-', '');
    copyAllTabs(format);
  }
  // 处理查看剪贴板历史
  else if (info.menuItemId === 'view-clipboard-history') {
    chrome.tabs.create({ url: 'clipboard-history.html' });
  }
  // 处理主菜单项点击
  else if (info.menuItemId === 'smartcopy-parent') {
    // 如果点击主菜单项，打开扩展弹出窗口
    chrome.action.openPopup();
  }
});

// 处理快捷键命令
chrome.commands.onCommand.addListener(async (command) => {
  console.log('快捷键触发:', command);
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    switch (command) {
      case 'copy-current-tab':
        if (tab) {
          await copySingleTab(tab);
        }
        break;
      case 'copy-all-tabs':
        await copyAllTabs();
        break;
      case 'toggle-auto-copy':
        if (tab && tab.id) {
          chrome.tabs.sendMessage(tab.id, { action: 'toggleAutoCopy' });
        }
        break;
      case 'open-clipboard-history':
        // 直接打开历史页面
        chrome.tabs.create({ url: 'clipboard-history.html' });
        break;
      case 'toggle-super-copy':
        if (tab && tab.id) {
          chrome.tabs.sendMessage(tab.id, { action: 'toggleSuperCopy' });
        }
        break;
      default:
        break;
    }
  } catch (error) {
    console.error('快捷键命令处理错误:', error);
  }
});

// 复制单个标签页
async function copySingleTab(tab, formatId) {
  // 如果没有指定格式，则使用存储的默认格式
  if (!formatId) {
    const settings = await chrome.storage.sync.get('copyFormat');
    formatId = settings.copyFormat || 'default';
  }

  let formattedText = '';

  // 根据不同格式处理
  if (formatId === 'json') {
    // 单个标签页的JSON格式
    formattedText = formatTabsAsJson([tab]);
  } else if (formatId === 'html-table-row') {
    // 单个标签页的HTML表格格式
    formattedText = formatTabsAsHtmlTable([tab]);
  } else {
    // 其他格式
    formattedText = formatTabInfo(tab, formatId);
  }

  await copyToClipboard(formattedText, '复制当前标签页');

  // 发送复制成功通知到当前标签页
  chrome.tabs.sendMessage(tab.id, {
    action: 'showCopyNotification',
    messageKey: 'copied_to_clipboard',
    useI18n: true
  }).catch(error => {
    console.log('发送通知失败，可能是内容脚本未加载:', error);
  });
}

// 复制所有标签页
async function copyAllTabs(formatId) {
  // 如果没有指定格式，则使用存储的默认格式
  if (!formatId) {
    const settings = await chrome.storage.sync.get('copyFormat');
    formatId = settings.copyFormat || 'default';
  }

  const tabs = await chrome.tabs.query({ currentWindow: true });
  let combinedText = '';

  // 根据不同格式处理
  if (formatId === 'json') {
    // 使用JSON格式
    combinedText = formatTabsAsJson(tabs);
  } else if (formatId === 'html-table') {
    // 使用HTML表格格式
    combinedText = formatTabsAsHtmlTable(tabs);
  } else {
    // 其他格式
    const formattedTexts = tabs.map(tab => formatTabInfo(tab, formatId));
    combinedText = formattedTexts.join('\n');
  }

  await copyToClipboard(combinedText, chrome.i18n.getMessage('copy_all_tabs_action'));

  // 发送复制成功通知到当前标签页
  const currentTab = await chrome.tabs.query({ active: true, currentWindow: true });
  if (currentTab.length > 0) {
    chrome.tabs.sendMessage(currentTab[0].id, {
      action: 'showCopyNotification',
      messageKey: 'copied_all_tabs',
      messageParams: [tabs.length.toString()], // 确保参数是字符串
      useI18n: true
    });
  }
}

// 切换自动复制功能
async function toggleAutoCopy() {
  try {
    console.log('正在切换自动复制功能...');
    const settings = await chrome.storage.sync.get('autoTextCopy');
    console.log('当前自动复制设置:', settings.autoTextCopy);

    const newSetting = !settings.autoTextCopy;
    console.log('新的自动复制设置将为:', newSetting);

    await chrome.storage.sync.set({ autoTextCopy: newSetting });
    console.log('已保存新设置到存储');

    // 通知所有标签页更新设置
    const tabs = await chrome.tabs.query({});
    console.log('将通知', tabs.length, '个标签页更新设置');

    for (const tab of tabs) {
      try {
        if (tab.url && tab.url.startsWith('http')) {
          await chrome.tabs.sendMessage(tab.id, {
            action: 'updateSettings',
            settings: { autoTextCopy: newSetting }
          });
          console.log('已通知标签页更新设置:', tab.id, tab.url);
        }
      } catch (err) {
        // 忽略无法发送消息的标签页（如chrome://页面）
        console.log('无法发送消息到标签页:', tab.id, tab.url);
      }
    }

    // 显示通知
    const currentTab = await chrome.tabs.query({ active: true, currentWindow: true });
    if (currentTab.length > 0) {
      try {
        await chrome.tabs.sendMessage(currentTab[0].id, {
          action: 'showCopyNotification',
          messageKey: newSetting ? 'auto_copy_enabled' : 'auto_copy_disabled',
          useI18n: true,
          type: newSetting ? 'success' : 'disabled'
        });
        console.log('已显示通知');
      } catch (err) {
        // 忽略无法发送消息的标签页
        console.log('无法显示通知:', err);
      }
    }

    // 返回当前状态
    return { enabled: newSetting };
  } catch (error) {
    console.error('切换自动复制功能时出错:', error);
    throw error;
  }
}

// 复制文本到剪贴板
async function copyToClipboard(text, source = chrome.i18n.getMessage('manual_copy')) {
  try {
    console.log('复制文本到剪贴板，来源:', source);

    // 添加到剪贴板历史记录
    const historyItem = await clipboardHistory.addItem(text, source);
    console.log('已添加到剪贴板历史记录:', historyItem ? historyItem.id : '添加失败');

    // 创建一个临时的内容脚本来访问剪贴板API
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab) {
      throw new Error('无法获取当前标签页');
    }

    // 确保标签页是可访问的（不是特殊页面如 chrome:// 页面）
    if (!tab.url.startsWith('http')) {
      // 对于特殊页面，我们需要创建一个临时标签页
      console.log('当前页面无法访问剪贴板，尝试其他方法');
      // 使用更简单的方法 - 发送消息到内容脚本
      chrome.tabs.sendMessage(tab.id, {
        action: 'copyToClipboard',
        text: text
      }).catch(err => {
        console.error('发送消息失败:', err);
      });
      return true;
    }

    // 对于普通网页，使用 scripting API
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: (textToCopy) => {
        return new Promise((resolve) => {
          navigator.clipboard.writeText(textToCopy)
            .then(() => resolve(true))
            .catch(err => {
              console.error('复制失败:', err);
              resolve(false);
            });
        });
      },
      args: [text]
    });

    console.log('文本已成功复制到剪贴板');
    return true;
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    // 尝试备用方法
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        chrome.tabs.sendMessage(tab.id, {
          action: 'copyToClipboard',
          text: text
        }).catch(() => {});
      }
      return true;
    } catch (backupError) {
      console.error('备用复制方法也失败:', backupError);
      return false;
    }
  }
}

// 监听来自弹出页面的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理语言检测消息
  if (message.action === 'languageDetected') {
    console.log('接收到语言检测消息:', message);

    // 获取当前语言设置
    chrome.storage.sync.get(['language'], (result) => {
      const storedLanguage = result.language || 'auto';

      // 如果设置为自动，则通知所有页面更新语言
      if (storedLanguage === 'auto') {
        console.log('语言设置为自动，通知所有页面更新语言:', message.detectedLanguage);

        // 通知所有页面更新语言
        chrome.runtime.sendMessage({
          action: 'settingsUpdated',
          settings: { detectedLanguage: message.detectedLanguage }
        }).catch(() => {
          // 忽略错误，可能是没有打开的页面
        });

        // 通知所有标签页更新语言
        chrome.tabs.query({}).then(tabs => {
          for (const tab of tabs) {
            try {
              chrome.tabs.sendMessage(tab.id, {
                action: 'settingsUpdated',
                settings: { detectedLanguage: message.detectedLanguage }
              }).catch(() => {
                // 忽略错误，可能是内容脚本还没有加载或者标签页不支持
              });
            } catch (e) {
              // 忽略可能的错误
            }
          }
        }).catch(error => {
          console.error('通知标签页更新语言失败:', error);
        });
      }
    });

    if (sendResponse) {
      sendResponse({ success: true });
    }
    return true;
  }

  if (message.action === 'copySingleTab') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length > 0) {
        copySingleTab(tabs[0]);
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '无法获取当前标签页' });
      }
    });
    return true; // 保持消息通道开放，以便异步响应
  }

  if (message.action === 'copyAllTabs') {
    copyAllTabs()
      .then(() => sendResponse({ success: true }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放，以便异步响应
  }

  if (message.action === 'toggleAutoCopy') {
    toggleAutoCopy()
      .then(result => sendResponse({ success: true, enabled: result.enabled }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放，以便异步响应
  }

  if (message.action === 'openClipboardHistory') {
    chrome.tabs.create({ url: 'clipboard-history.html' });
    sendResponse({ success: true });
    return true;
  }

  if (message.action === 'addToClipboardHistory') {
    if (message.text) {
      console.log('添加到剪贴板历史记录:', message.text.substring(0, 30) + (message.text.length > 30 ? '...' : ''), '来源:', message.source);

      // 处理源标签，确保使用正确的国际化消息
      let sourceText = message.source || chrome.i18n.getMessage('auto_copy_text');

      // 检查是否包含中文字符
      if (/[\u4e00-\u9fa5]/.test(sourceText)) {
        // 如果是中文源标签，根据内容判断类型
        if (sourceText.includes('自动') || sourceText.includes('自動') ||
            sourceText.includes('选中') || sourceText.includes('選中')) {
          // 检查是否是自动复制源格式（包含格式信息）
          if (sourceText.includes('(') && sourceText.includes(')')) {
            // 尝试提取格式信息
            const formatMatch = sourceText.match(/\(([^)]+)\)/);
            if (formatMatch && formatMatch[1]) {
              const formatName = formatMatch[1];
              // 确定格式类型
              let formatKey = '';
              if (formatName.includes('纯文本') || formatName.includes('純文本')) {
                formatKey = 'plain_text';
              } else if (formatName.toLowerCase().includes('markdown')) {
                formatKey = 'markdown';
              }

              if (formatKey) {
                // 使用国际化消息重新构建源文本
                const formatText = chrome.i18n.getMessage(formatKey);
                sourceText = chrome.i18n.getMessage('auto_copy_source', [formatText]);
                console.log('Setting sourceText with format in background.js:', formatKey, formatText);
              } else {
                // 如果无法确定格式类型，使用通用自动复制文本
                sourceText = chrome.i18n.getMessage('auto_copy_text');
              }
            } else {
              // 如果无法提取格式信息，使用通用自动复制文本
              sourceText = chrome.i18n.getMessage('auto_copy_text');
            }
          } else {
            // 处理没有格式信息的自动复制文本
            sourceText = chrome.i18n.getMessage('auto_copy_text');
          }
        }
      }

      clipboardHistory.addItem(message.text, sourceText)
        .then((newItem) => {
          console.log('成功添加到剪贴板历史记录:', newItem ? newItem.id : 'unknown');
          if (sendResponse) {
            sendResponse({ success: true, item: newItem });
          }
        })
        .catch(error => {
          console.error('添加到剪贴板历史记录失败:', error);
          if (sendResponse) {
            sendResponse({ success: false, error: error.message });
          }
        });
      return true;
    }
  }

  if (message.action === 'clipboardHistoryCleared') {
    console.log('接收到剪贴板历史记录已清空的通知');
    // 确保剪贴板历史记录在背景脚本中也被清空
    clipboardHistory.clear()
      .then(() => {
        console.log('背景脚本中的剪贴板历史记录已清空');
        if (sendResponse) {
          sendResponse({ success: true });
        }
      })
      .catch(error => {
        console.error('清空背景脚本中的剪贴板历史记录失败:', error);
        if (sendResponse) {
          sendResponse({ success: false, error: error.message });
        }
      });
    return true;
  }

  // 处理剪贴板历史记录更新消息
  if (message.action === 'clipboardHistoryUpdated') {
    console.log('接收到剪贴板历史记录更新通知:', message.operation);

    // 确保背景脚本中的历史记录也被更新
    clipboardHistory.loadHistory()
      .then(() => {
        console.log('背景脚本中的剪贴板历史记录已更新');
        if (sendResponse) {
          sendResponse({ success: true });
        }
      })
      .catch(error => {
        console.error('更新背景脚本中的剪贴板历史记录失败:', error);
        if (sendResponse) {
          sendResponse({ success: false, error: error.message });
        }
      });
    return true;
  }

  // 处理设置更新消息
  if (message.action === 'settingsUpdated') {
    console.log('接收到设置更新通知:', message.settings);

    // 广播设置更新到所有打开的弹出窗口
    chrome.runtime.sendMessage({
      action: 'settingsUpdated',
      settings: message.settings
    }).catch(error => {
      // 忽略错误，可能是没有打开的弹出窗口
      console.log('广播设置更新失败，可能没有打开的弹出窗口:', error);
    });

    // 如果语言设置已更改，刷新上下文菜单
    if (message.settings && message.settings.language) {
      console.log('语言设置已更改，刷新上下文菜单');
      console.log('语言设置已更改为:', message.settings.language);

      // 刷新上下文菜单以确保国际化正确应用
      refreshContextMenus();
    }

    // 更新存储中的设置
    if (message.settings) {
      chrome.storage.sync.set(message.settings)
        .then(() => {
          console.log('已更新存储中的设置');
          sendResponse({ success: true });
        })
        .catch(error => {
          console.error('更新存储中的设置失败:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }
  }

  // 处理超级复制功能切换消息
  if (message.action === 'toggleSuperCopy') {
    console.log('接收到超级复制功能切换消息:', message);

    // 获取当前域名和状态
    const domain = message.domain;
    const enabled = message.enabled;

    if (domain) {
      // 获取当前设置
      chrome.storage.sync.get(['superCopySiteSettings'])
        .then(data => {
          // 更新设置
          const superCopySiteSettings = data.superCopySiteSettings || {};
          superCopySiteSettings[domain] = enabled;

          // 保存到存储
          return chrome.storage.sync.set({ superCopySiteSettings });
        })
        .then(() => {
          console.log(`已更新域名 ${domain} 的超级复制设置为 ${enabled ? '已启用' : '已禁用'}`);

          // 广播设置变化到所有标签页
          return chrome.tabs.query({});
        })
        .then(tabs => {
          // 向所有标签页发送消息
          for (const tab of tabs) {
            try {
              chrome.tabs.sendMessage(tab.id, {
                action: 'superCopySettingChanged',
                domain: domain,
                enabled: enabled
              }).catch(() => {
                // 忽略错误，可能是内容脚本还没有加载或者标签页不支持
              });
            } catch (e) {
              // 忽略可能的错误
            }
          }

          if (sendResponse) {
            sendResponse({ success: true });
          }
        })
        .catch(error => {
          console.error('处理超级复制功能切换消息失败:', error);
          if (sendResponse) {
            sendResponse({ success: false, error: error.message });
          }
        });

      return true; // 保持消息通道开放，以便异步响应
    }
  }
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, areaName) => {
  if (areaName === 'sync') {
    console.log('存储变化:', changes);

    // 提取变化的设置
    const updatedSettings = {};
    if (changes.autoTextCopy) {
      updatedSettings.autoTextCopy = changes.autoTextCopy.newValue;
    }
    if (changes.copyFormat) {
      updatedSettings.copyFormat = changes.copyFormat.newValue;
    }
    if (changes.minCharacters) {
      updatedSettings.minCharacters = changes.minCharacters.newValue;
    }
    if (changes.maxHistoryItems) {
      updatedSettings.maxHistoryItems = changes.maxHistoryItems.newValue;
    }

    if (changes.language) {
      updatedSettings.language = changes.language.newValue;

      // 如果语言设置为自动，则使用浏览器语言
      if (changes.language.newValue === 'auto') {
        const browserLanguage = getBrowserLanguage();
        console.log('自动语言模式，使用浏览器语言:', browserLanguage);
        // 不需要更新存储，因为我们希望保持'auto'设置
      }

      // 无论是什么语言设置，都刷新上下文菜单以确保国际化正确应用
      console.log('语言设置已更改，刷新上下文菜单');
      refreshContextMenus();
    }

    // 如果有设置变化，广播到所有页面
    if (Object.keys(updatedSettings).length > 0) {
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: updatedSettings
      }).catch(() => {
        // 忽略错误，可能是没有打开的页面
      });
    }

    // 处理超级复制站点设置的变化
    if (changes.superCopySiteSettings) {
      console.log('超级复制站点设置变化:', changes.superCopySiteSettings);

      // 获取所有标签页
      chrome.tabs.query({}).then(tabs => {
        // 向所有标签页发送消息
        for (const tab of tabs) {
          try {
            chrome.tabs.sendMessage(tab.id, {
              action: 'superCopySiteSettingsChanged',
              settings: changes.superCopySiteSettings.newValue
            }).catch(() => {
              // 忽略错误，可能是内容脚本还没有加载或者标签页不支持
            });
          } catch (e) {
            // 忽略可能的错误
          }
        }
      }).catch(error => {
        console.error('广播超级复制设置变化失败:', error);
      });
    }
  }
});
