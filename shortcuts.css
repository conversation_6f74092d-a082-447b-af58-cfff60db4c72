/* 快捷键样式 - 可在任何页面中引用 */
.shortcuts-container {
  background-color: #e8f0fe;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #1a73e8;
}

.shortcuts-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #1a73e8;
  font-size: 16px;
  font-weight: 500;
}

.shortcuts-heading {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
  color: #1a73e8;
  font-weight: 500;
}

.shortcuts-heading::before {
  content: "⌨️";
  margin-right: 8px;
  font-size: 20px;
}

.shortcut-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.shortcut-row:last-child {
  margin-bottom: 0;
}

.shortcut-label {
  font-weight: 500;
  color: #202124;
  flex: 1;
}

.shortcut-combo {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.key {
  display: inline-block;
  background-color: #f1f3f4;
  color: #202124;
  padding: 5px 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #dadce0;
  min-width: 20px;
  text-align: center;
}

.key-plus {
  color: #5f6368;
  font-weight: 500;
}

.os-tag {
  color: #5f6368;
  font-size: 12px;
  margin-left: 5px;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .shortcut-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .shortcut-label {
    margin-bottom: 8px;
  }

  .shortcut-combo {
    width: 100%;
    justify-content: flex-start;
  }
}

/* 蓝色主题变体 */
.shortcuts-blue {
  background-color: #e8f0fe;
  border-left: 4px solid #1a73e8;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .shortcuts-container {
    background-color: #292a2d;
  }

  .shortcut-row {
    background-color: #35363a;
  }

  .shortcut-label {
    color: #e8eaed;
  }

  .key {
    background-color: #202124;
    color: #e8eaed;
    border-color: #5f6368;
  }

  .key-plus,
  .os-tag {
    color: #9aa0a6;
  }

  .shortcuts-blue {
    background-color: #1a2f44;
    border-left-color: #8ab4f8;
  }
}
