// 剪贴板历史记录UI交互脚本
import { clipboardHistory } from './clipboard-history.js';

// 获取国际化消息的辅助函数
function getMessage(key, params) {
  // 使用全局 getMessage 函数，它会处理语言切换和缓存
  if (window.getMessage) {
    const result = window.getMessage(key, params);
    // 检查结果是否包含未替换的占位符
    if (result && result.includes('($1)') && params && params.length > 0) {
      console.log('检测到未替换的占位符 ($1)，手动替换:', result, params);
      // 手动替换占位符
      return result.replace('($1)', `(${params[0]})`);
    }
    return result;
  }

  // 回退到 Chrome 的 i18n API
  if (params) {
    const result = chrome.i18n.getMessage(key, params) || key;
    // 检查结果是否包含未替换的占位符
    if (result && result.includes('($1)') && params && params.length > 0) {
      console.log('Chrome i18n API 未替换占位符，手动替换:', result, params);
      // 手动替换占位符
      return result.replace('($1)', `(${params[0]})`);
    }
    return result;
  }

  return chrome.i18n.getMessage(key) || key;
}

// DOM元素
const historyListElement = document.getElementById('history-list');
const emptyStateElement = document.getElementById('empty-state');
const loadingElement = document.getElementById('loading');
const prevPageButton = document.getElementById('prev-page');
const nextPageButton = document.getElementById('next-page');
const pageInfoElement = document.getElementById('page-info');
const itemsPerPageSelect = document.getElementById('items-per-page');
const refreshButton = document.getElementById('refresh-btn');
const clearHistoryButton = document.getElementById('clear-history-btn');
const notificationElement = document.getElementById('notification');
const notificationMessageElement = document.getElementById('notification-message');
const notificationCloseButton = document.getElementById('notification-close');

// 状态变量
let allHistoryItems = [];
let currentPage = 1;
let itemsPerPage = 10;
let isLoading = false;

// 初始化
async function initialize() {
  console.log('初始化剪贴板历史记录页面');

  // 加载保存的设置
  const settings = await chrome.storage.sync.get('historyItemsPerPage');
  if (settings.historyItemsPerPage) {
    itemsPerPage = parseInt(settings.historyItemsPerPage);
    itemsPerPageSelect.value = itemsPerPage.toString();
  }

  // 添加事件监听器
  prevPageButton.addEventListener('click', handlePrevPage);
  nextPageButton.addEventListener('click', handleNextPage);
  itemsPerPageSelect.addEventListener('change', handleItemsPerPageChange);
  refreshButton.addEventListener('click', handleRefresh);
  clearHistoryButton.addEventListener('click', handleClearHistory);
  notificationCloseButton.addEventListener('click', hideNotification);

  // 加载历史记录
  await loadHistoryItems();
}

// 加载历史记录项
async function loadHistoryItems() {
  try {
    setLoading(true);

    // 获取所有历史记录
    allHistoryItems = await clipboardHistory.getAll();
    console.log(`加载了 ${allHistoryItems.length} 条历史记录`);

    // 处理所有历史记录的源标签
    allHistoryItems = allHistoryItems.map(item => {
      // 创建一个新对象，避免修改原始对象
      const newItem = {...item};

      if (newItem.source) {
        // 检查是否包含中文字符
        if (/[\u4e00-\u9fa5]/.test(newItem.source)) {
          // 如果是中文源标签，根据内容判断类型
          if (newItem.source === '自动复制选中文本' ||
              newItem.source === '自動複製選中文本' ||
              newItem.source === '选中文字自动复制' ||
              newItem.source === '選中文字自動複製' ||
              newItem.source === '自动复制选中文本 ()' ||
              newItem.source === '自動複製選中文本 ()') {
            newItem.source = getMessage('auto_copy_text');
          } else if (newItem.source.includes('(') && newItem.source.includes(')')) {
            // 尝试提取格式信息
            const formatMatch = newItem.source.match(/\(([^)]+)\)/);
            if (formatMatch && formatMatch[1]) {
              const formatName = formatMatch[1];
              // 确定格式类型
              let formatKey = '';
              if (formatName.includes('纯文本') || formatName.includes('純文本')) {
                formatKey = 'plain_text';
              } else if (formatName.toLowerCase().includes('markdown')) {
                formatKey = 'markdown';
              }

              if (formatKey) {
                // 使用国际化消息重新构建源文本
                const formatText = getMessage(formatKey);
                newItem.source = getMessage('auto_copy_source', [formatText]);
                console.log('Setting source with format:', formatKey, formatText);
              } else {
                // 如果无法确定格式类型，使用通用自动复制文本
                newItem.source = getMessage('auto_copy_text');
              }
            } else {
              // 如果无法提取格式信息，使用通用自动复制文本
              newItem.source = getMessage('auto_copy_text');
            }
          }
        }
      }

      return newItem;
    });

    // 显示历史记录
    displayCurrentPage();

    // 更新空状态显示
    if (allHistoryItems.length === 0) {
      emptyStateElement.style.display = 'flex';
    } else {
      emptyStateElement.style.display = 'none';
    }
  } catch (error) {
    console.error('加载历史记录失败:', error);
    showNotification(getMessage('refresh_failed'), 'error');
  } finally {
    setLoading(false);
  }
}

// 设置加载状态
function setLoading(loading) {
  isLoading = loading;
  loadingElement.style.display = loading ? 'flex' : 'none';

  // 禁用或启用按钮
  prevPageButton.disabled = loading || currentPage === 1;
  nextPageButton.disabled = loading || currentPage >= Math.ceil(allHistoryItems.length / itemsPerPage);
  refreshButton.disabled = loading;
  clearHistoryButton.disabled = loading;
}

// 显示当前页的记录
function displayCurrentPage() {
  console.log('显示当前页记录，页码:', currentPage, '每页条数:', itemsPerPage);

  // 清空现有列表
  historyListElement.innerHTML = '';

  // 如果没有记录，直接返回
  if (allHistoryItems.length === 0) {
    console.log('没有历史记录');
    updatePagination();
    return;
  }

  // 计算当前页的起始和结束索引
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, allHistoryItems.length);
  console.log(`显示记录从 ${startIndex} 到 ${endIndex}，共 ${allHistoryItems.length} 条`);

  // 创建文档片段以提高性能
  const fragment = document.createDocumentFragment();

  // 添加当前页的记录
  for (let i = startIndex; i < endIndex; i++) {
    const item = allHistoryItems[i];
    const itemElement = createHistoryItem(item);
    fragment.appendChild(itemElement);
  }

  // 一次性添加所有元素
  historyListElement.appendChild(fragment);

  // 更新分页信息
  updatePagination();

  // 更新动态元素的国际化文本
  if (window.updateDynamicElements) {
    window.updateDynamicElements();
  }
}

// 创建历史记录项元素
function createHistoryItem(item) {
  // 主容器
  const itemElement = document.createElement('div');
  itemElement.className = 'history-item';
  itemElement.dataset.id = item.id;

  // 根据来源设置不同的样式
  if (item.source) {
    itemElement.classList.add(`source-${item.source.replace(/\s+/g, '-').toLowerCase()}`);
  }

  // 创建标题栏
  const titleBar = document.createElement('div');
  titleBar.className = 'item-title-bar';

  // 创建标题左侧部分（包含来源标签）
  const titleLeft = document.createElement('div');
  titleLeft.className = 'title-left';

  // 创建来源标签
  const sourceTag = document.createElement('span');
  sourceTag.className = 'source-tag';

  // 根据来源设置不同的样式和文本
  let sourceText = item.source || getMessage('manual_copy');
  let sourceClass = '';

  // 首先检查文本内容是否包含Markdown特征
  if (item.text && hasMarkdownFeatures(item.text)) {
    console.log('检测到历史记录项包含Markdown特征，设置格式为markdown');
    const formatKey = 'markdown';
    const formatText = getMessage(formatKey);
    sourceText = getMessage('auto_copy_source', [formatText]);
    console.log('已将历史记录项的格式设置为Markdown:', sourceText);
  }
  // 直接替换所有可能的中文源标签
  else if (sourceText === '自动复制选中文本' ||
      sourceText === '自動複製選中文本' ||
      sourceText === '选中文字自动复制' ||
      sourceText === '選中文字自動複製' ||
      sourceText === '自动复制选中文本 ()' ||
      sourceText === '自動複製選中文本 ()' ||
      sourceText === '自动复制选中文本 (纯文本)' ||
      sourceText === '自動複製選中文本 (純文本)' ||
      sourceText === '自动复制选中文本 (Markdown)' ||
      sourceText === '自動複製選中文本 (Markdown)') {

    // 检查是否包含格式信息
    if (sourceText.includes('(') && sourceText.includes(')')) {
      // 尝试提取格式信息
      const formatMatch = sourceText.match(/\(([^)]+)\)/);
      if (formatMatch && formatMatch[1]) {
        const formatName = formatMatch[1];
        // 确定格式类型
        let formatKey = '';
        if (formatName.includes('纯文本') || formatName.includes('純文本') ||
            formatName.toLowerCase().includes('plain text')) {
          formatKey = 'plain_text';
        } else if (formatName.toLowerCase().includes('markdown')) {
          formatKey = 'markdown';
        }

        // 如果无法从格式名称确定格式类型，尝试从内容判断
        if (!formatKey && item.text) {
          // 检查是否包含Markdown语法特征
          const hasMarkdownSyntax = hasMarkdownFeatures(item.text);

          if (hasMarkdownSyntax) {
            console.log('检测到文本内容包含Markdown语法特征，设置格式为markdown');
            formatKey = 'markdown';
          }
        }

        if (formatKey) {
          // 使用国际化消息重新构建源文本
          const formatText = getMessage(formatKey);
          sourceText = getMessage('auto_copy_source', [formatText]);
          console.log('Setting sourceText with format:', formatKey, formatText);
        } else {
          // 如果无法确定格式类型，使用通用自动复制文本
          sourceText = getMessage('auto_copy_text');
        }
      } else {
        // 如果无法提取格式信息，使用通用自动复制文本
        sourceText = getMessage('auto_copy_text');
      }
    } else {
      // 没有格式信息，使用通用自动复制文本
      sourceText = getMessage('auto_copy_text');
    }
  }

  // 通用处理：检查是否包含任何中文字符
  if (/[\u4e00-\u9fa5]/.test(sourceText)) {
    // 如果是中文源标签，检查是否包含格式信息
    const originalSourceText = sourceText; // 保存原始文本

    // 先设置为基本的自动复制文本
    sourceText = getMessage('auto_copy_text');

    // 如果原始文本包含格式信息，尝试提取并使用正确的格式
    if (originalSourceText.includes('(') && originalSourceText.includes(')')) {
      const formatMatch = originalSourceText.match(/\(([^)]+)\)/);
      if (formatMatch && formatMatch[1]) {
        const formatName = formatMatch[1];
        let formatKey = '';

        // 确定格式类型
        if (formatName.includes('纯文本') || formatName.includes('純文本')) {
          formatKey = 'plain_text';
        } else if (formatName.toLowerCase().includes('markdown')) {
          formatKey = 'markdown';
        }

        if (formatKey) {
          const formatText = getMessage(formatKey);
          sourceText = getMessage('auto_copy_source', [formatText]);
          console.log('Setting sourceText with format (1):', formatKey, formatText);
        }
      }
    }
  } else {
    // 英文界面下的处理
    if (sourceText.includes('(') && sourceText.includes(')')) {
      const formatMatch = sourceText.match(/\(([^)]+)\)/);
      if (formatMatch && formatMatch[1]) {
        const formatName = formatMatch[1];
        let formatKey = '';

        if (formatName.toLowerCase().includes('plain') ||
            formatName.toLowerCase().includes('text')) {
          formatKey = 'plain_text';
        } else if (formatName.toLowerCase().includes('markdown')) {
          formatKey = 'markdown';
        }

        if (formatKey) {
          const formatText = getMessage(formatKey);
          sourceText = getMessage('auto_copy_source', [formatText]);
          console.log('Setting sourceText with format (2):', formatKey, formatText);
        }
      }
    } else if (sourceText.toLowerCase().includes('auto') &&
               (sourceText.toLowerCase().includes('copy') ||
                sourceText.toLowerCase().includes('copied'))) {
      sourceText = getMessage('auto_copy_text');
    }
  }

  // 检查源文本并设置适当的类
  if (sourceText.includes(getMessage('auto_copy_text')) ||
      sourceText.includes('自动') ||
      sourceText.includes('自動') ||
      sourceText.includes('Auto') ||
      sourceText.includes('选中') ||
      sourceText.includes('選中')) {
    sourceClass = 'auto';

    // 检查是否包含格式信息
    if (sourceText.includes('(') && sourceText.includes(')')) {
      // 检查是否包含未替换的占位符 ($1)
      if (sourceText.includes('($1)')) {
        console.log('检测到未替换的占位符 ($1)，尝试修复');

        // 检查文本内容是否包含Markdown特征
        let formatKey = 'plain_text'; // 默认为纯文本

        if (item && item.text) {
          // 检查是否包含Markdown语法特征
          const hasMarkdownSyntax = hasMarkdownFeatures(item.text);

          if (hasMarkdownSyntax) {
            console.log('检测到文本内容包含Markdown语法特征，设置格式为markdown');
            formatKey = 'markdown';
          }
        }

        const formatText = getMessage(formatKey);
        sourceText = getMessage('auto_copy_source', [formatText]);
        console.log('修复后的源文本:', sourceText);
      } else {
        // 已经在前面处理过，保持不变
      }
    } else {
      // 替换源文本为当前语言的国际化消息
      sourceText = getMessage('auto_copy_text');
    }
  } else if (sourceText.includes(getMessage('copy_current_tab')) ||
             sourceText.includes('当前标签') ||
             sourceText.includes('Current tab')) {
    sourceClass = 'current-tab';
    // 替换源文本为当前语言的国际化消息
    sourceText = getMessage('copy_current_tab');
  } else if (sourceText.includes(getMessage('copy_all_tabs')) ||
             sourceText.includes('所有标签') ||
             sourceText.includes('All tabs')) {
    sourceClass = 'all-tabs';
    // 替换源文本为当前语言的国际化消息
    sourceText = getMessage('copy_all_tabs');
  }

  sourceTag.textContent = sourceText;
  if (sourceClass) {
    sourceTag.classList.add(sourceClass);
  }

  titleLeft.appendChild(sourceTag);

  // 创建时间戳
  const timestamp = document.createElement('span');
  timestamp.className = 'timestamp';

  // 格式化时间
  const date = new Date(item.timestamp);
  const formattedDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
  timestamp.textContent = formattedDate;

  // 组装标题栏
  titleBar.appendChild(titleLeft);
  titleBar.appendChild(timestamp);
  itemElement.appendChild(titleBar);

  // 创建内容容器
  const contentContainer = document.createElement('div');
  contentContainer.className = 'content-container';

  // 创建内容元素
  const contentElement = document.createElement('pre');
  contentElement.className = 'item-content';
  contentElement.textContent = item.text;

  // 添加内容到容器
  contentContainer.appendChild(contentElement);

  // 如果内容超过一定高度，添加展开按钮
  if (item.text.length > 150 || item.text.split('\n').length > 5) {
    const expandButtonContainer = document.createElement('div');
    expandButtonContainer.className = 'expand-button-container';

    const expandButton = document.createElement('button');
    expandButton.className = 'expand-btn';
    expandButton.textContent = getMessage('expand');
    expandButton.addEventListener('click', () => {
      if (contentElement.classList.contains('expanded')) {
        contentElement.classList.remove('expanded');
        expandButton.textContent = getMessage('expand');
      } else {
        contentElement.classList.add('expanded');
        expandButton.textContent = getMessage('collapse');
      }
    });

    expandButtonContainer.appendChild(expandButton);
    contentContainer.appendChild(expandButtonContainer);
  }

  itemElement.appendChild(contentContainer);

  // 创建操作按钮
  const actionsContainer = document.createElement('div');
  actionsContainer.className = 'item-actions';

  // 复制按钮
  const copyButton = document.createElement('button');
  copyButton.className = 'action-btn copy-btn';
  copyButton.innerHTML = `<span>${getMessage('copy')}</span>`;
  copyButton.addEventListener('click', () => handleCopy(item));

  // 删除按钮
  const deleteButton = document.createElement('button');
  deleteButton.className = 'action-btn delete-btn';
  deleteButton.innerHTML = `<span>${getMessage('delete')}</span>`;
  deleteButton.addEventListener('click', () => handleDelete(item.id));

  // 添加按钮到操作容器
  actionsContainer.appendChild(copyButton);
  actionsContainer.appendChild(deleteButton);

  // 添加操作容器到项目
  itemElement.appendChild(actionsContainer);

  return itemElement;
}

// 更新分页信息
function updatePagination() {
  const totalPages = Math.max(1, Math.ceil(allHistoryItems.length / itemsPerPage));
  pageInfoElement.textContent = getMessage('page_info').replace('$CURRENT$', currentPage).replace('$TOTAL$', totalPages);

  // 更新按钮状态
  prevPageButton.disabled = currentPage === 1;
  nextPageButton.disabled = currentPage >= totalPages;
}

// 处理上一页
function handlePrevPage() {
  if (currentPage > 1) {
    currentPage--;
    displayCurrentPage();
    // 滚动到顶部
    window.scrollTo(0, 0);
  }
}

// 处理下一页
function handleNextPage() {
  const totalPages = Math.ceil(allHistoryItems.length / itemsPerPage);
  if (currentPage < totalPages) {
    currentPage++;
    displayCurrentPage();
    // 滚动到顶部
    window.scrollTo(0, 0);
  }
}

// 处理每页显示数量变更
async function handleItemsPerPageChange() {
  const newItemsPerPage = parseInt(itemsPerPageSelect.value);
  if (newItemsPerPage !== itemsPerPage) {
    itemsPerPage = newItemsPerPage;

    // 保存设置
    await chrome.storage.sync.set({ historyItemsPerPage: itemsPerPage });

    // 重置到第一页
    currentPage = 1;

    // 重新显示
    displayCurrentPage();
  }
}

// 处理刷新
async function handleRefresh() {
  try {
    setLoading(true);

    // 直接从剪贴板历史记录管理类获取最新数据
    allHistoryItems = await clipboardHistory.getAll();
    console.log(`刷新：加载了 ${allHistoryItems.length} 条历史记录`);

    // 处理所有历史记录的源标签
    allHistoryItems = allHistoryItems.map(item => {
      // 创建一个新对象，避免修改原始对象
      const newItem = {...item};

      if (newItem.source) {
        // 检查是否包含中文字符
        if (/[\u4e00-\u9fa5]/.test(newItem.source)) {
          // 如果是中文源标签，根据内容判断类型
          if (newItem.source === '自动复制选中文本' ||
              newItem.source === '自動複製選中文本' ||
              newItem.source === '选中文字自动复制' ||
              newItem.source === '選中文字自動複製' ||
              newItem.source === '自动复制选中文本 ()' ||
              newItem.source === '自動複製選中文本 ()') {
            newItem.source = getMessage('auto_copy_text');
          } else if (newItem.source.includes('(') && newItem.source.includes(')')) {
            // 尝试提取格式信息
            const formatMatch = newItem.source.match(/\(([^)]+)\)/);
            if (formatMatch && formatMatch[1]) {
              const formatName = formatMatch[1];
              // 确定格式类型
              let formatKey = '';
              if (formatName.includes('纯文本') || formatName.includes('純文本')) {
                formatKey = 'plain_text';
              } else if (formatName.toLowerCase().includes('markdown')) {
                formatKey = 'markdown';
              }

              if (formatKey) {
                // 使用国际化消息重新构建源文本
                const formatText = getMessage(formatKey);
                newItem.source = getMessage('auto_copy_source', [formatText]);
                console.log('Setting newItem.source with format:', formatKey, formatText);
              } else {
                // 如果无法确定格式类型，使用通用自动复制文本
                newItem.source = getMessage('auto_copy_text');
              }
            } else {
              // 如果无法提取格式信息，使用通用自动复制文本
              newItem.source = getMessage('auto_copy_text');
            }
          }
        }
      }

      return newItem;
    });

    // 显示历史记录
    displayCurrentPage();

    // 更新空状态显示
    if (allHistoryItems.length === 0) {
      emptyStateElement.style.display = 'flex';
    } else {
      emptyStateElement.style.display = 'none';
    }

    showNotification(getMessage('history_refreshed'));
  } catch (error) {
    console.error('刷新历史记录失败:', error);
    showNotification(getMessage('refresh_failed'), 'error');
  } finally {
    setLoading(false);
  }
}

// 处理清空历史
async function handleClearHistory() {
  try {
    // 使用自定义对话框，完全控制标题和内容
    const title = getMessage('dialog_title_clear_history'); // 使用专门为对话框设计的标题
    const message = getMessage('extension_prompt_clear_history');
    const confirmText = getMessage('confirm');
    const cancelText = getMessage('cancel');

    // 检查自定义对话框函数是否可用
    if (typeof window.customConfirmDialog === 'function') {
      // 使用自定义对话框
      const confirmed = await window.customConfirmDialog(title, message, confirmText, cancelText);
      if (!confirmed) {
        return; // 用户取消
      }
    } else {
      // 回退到原生confirm
      if (!confirm(getMessage('extension_prompt_clear_history'))) {
        return;
      }
    }

    // 用户确认，继续清空历史
    setLoading(true);

    // 清空历史记录
    await clipboardHistory.clear();

    // 重新加载历史记录以确保完全清空
    await clipboardHistory.loadHistory();

    // 更新UI
    allHistoryItems = [];
    currentPage = 1;
    displayCurrentPage();
    emptyStateElement.style.display = 'flex';

    // 显示成功通知
    showNotification(getMessage('history_cleared'));

    // 通知背景脚本历史记录已清空
    try {
      chrome.runtime.sendMessage({ action: 'clipboardHistoryCleared' });
    } catch (err) {
      console.error('通知背景脚本失败:', err);
    }
  } catch (error) {
    console.error('清空历史记录失败:', error);
    showNotification(getMessage('clear_failed'), 'error');
  } finally {
    setLoading(false);
  }
}

// 处理复制
async function handleCopy(item) {
  try {
    // 复制到剪贴板
    await navigator.clipboard.writeText(item.text);
    showNotification(getMessage('copied_to_clipboard'));
  } catch (error) {
    console.error('复制失败:', error);

    // 尝试使用备用方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = item.text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const success = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (success) {
        showNotification(getMessage('copied_to_clipboard'));
      } else {
        throw new Error('复制命令失败');
      }
    } catch (backupError) {
      console.error('备用复制方法也失败:', backupError);
      showNotification(getMessage('copy_failed').replace('$ERROR$', ''), 'error');
    }
  }
}

// 处理删除
async function handleDelete(id) {
  try {
    await clipboardHistory.deleteItem(id);

    // 从列表中移除
    allHistoryItems = allHistoryItems.filter(item => item.id !== id);

    // 如果当前页没有项目了，且不是第一页，则返回上一页
    const totalPages = Math.ceil(allHistoryItems.length / itemsPerPage);
    if (currentPage > totalPages && currentPage > 1) {
      currentPage--;
    }

    // 重新显示
    displayCurrentPage();

    // 如果没有记录了，显示空状态
    if (allHistoryItems.length === 0) {
      emptyStateElement.style.display = 'flex';
    }

    showNotification(getMessage('item_deleted'));
  } catch (error) {
    console.error('删除历史记录失败:', error);
    showNotification(getMessage('delete_failed'), 'error');
  }
}

// 显示通知
function showNotification(message, type = 'success') {
  notificationMessageElement.textContent = message;

  // 添加图标
  let iconElement = notificationElement.querySelector('.notification-icon');
  if (!iconElement) {
    iconElement = document.createElement('span');
    iconElement.className = 'notification-icon';
    notificationElement.insertBefore(iconElement, notificationMessageElement);
  }

  // 根据类型设置图标和样式
  switch (type) {
    case 'success':
      iconElement.textContent = '✓';
      notificationElement.className = 'notification success';
      break;
    case 'error':
      iconElement.textContent = '✗';
      notificationElement.className = 'notification error';
      break;
    case 'warning':
      iconElement.textContent = '⚠';
      notificationElement.className = 'notification warning';
      break;
    case 'info':
      iconElement.textContent = 'ℹ';
      notificationElement.className = 'notification info';
      break;
    default:
      iconElement.textContent = '✓';
      notificationElement.className = 'notification success';
  }

  notificationElement.classList.add('show');

  // 3秒后自动隐藏
  setTimeout(() => {
    hideNotification();
  }, 3000);

  // 添加通知样式
  const styleElement = document.getElementById('notification-styles');
  if (!styleElement) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
      .notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        gap: 12px;
        z-index: 1000;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        font-weight: 500;
      }

      .notification.show {
        opacity: 1;
        transform: translateY(0);
      }

      .notification .notification-icon {
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .notification .message {
        flex: 1;
      }

      .notification .close {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 18px;
        opacity: 0.7;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        width: 24px;
        height: 24px;
        transition: opacity 0.2s;
      }

      .notification .close:hover {
        opacity: 1;
      }

      .notification.success {
        background-color: rgba(26, 115, 232, 0.95);
        color: white;
      }

      .notification.error {
        background-color: rgba(217, 48, 37, 0.95);
        color: white;
      }

      .notification.warning {
        background-color: rgba(251, 188, 5, 0.95);
        color: #202124;
      }

      .notification.info {
        background-color: rgba(95, 99, 104, 0.95);
        color: white;
      }
    `;
    document.head.appendChild(style);
  }
}

// 隐藏通知
function hideNotification() {
  notificationElement.classList.remove('show');
}

// 辅助函数：补零
function padZero(num) {
  return num.toString().padStart(2, '0');
}

// 检查文本是否包含Markdown特征
function hasMarkdownFeatures(text) {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // 检查是否包含Markdown语法特征
  return (
    /(\*\*|__).+?(\*\*|__)/g.test(text) || // 粗体
    /(\*|_).+?(\*|_)/g.test(text) || // 斜体
    /\[.+?\]\(.+?\)/g.test(text) || // 链接
    /!\[.+?\]\(.+?\)/g.test(text) || // 图片
    /^#{1,6}\s.+$/gm.test(text) || // 标题
    /^>\s.+$/gm.test(text) || // 引用
    /^-\s.+$/gm.test(text) || // 无序列表
    /^[0-9]+\.\s.+$/gm.test(text) || // 有序列表
    /```[\s\S]*?```/g.test(text) || // 代码块
    /^```\w*$/m.test(text) // 代码块开始标记
  );
}

// 初始化页面
document.addEventListener('DOMContentLoaded', initialize);
