/**
 * 内容脚本的国际化辅助函数
 * 这个文件提供了与 i18n.js 类似的功能，但专门用于内容脚本
 */

// 当前使用的语言
let currentDisplayLanguage = 'auto';

// 预加载的消息缓存
const messagesCache = {
  en: {},
  zh_CN: {},
  zh_TW: {}
};

// 标记是否已加载消息
let messagesLoaded = false;

// 加载消息文件
async function loadMessages() {
  try {
    // 加载英文消息
    const enResponse = await fetch(chrome.runtime.getURL('_locales/en/messages.json'));
    messagesCache.en = await enResponse.json();

    // 加载简体中文消息
    const zhCNResponse = await fetch(chrome.runtime.getURL('_locales/zh_CN/messages.json'));
    messagesCache.zh_CN = await zhCNResponse.json();

    // 加载繁体中文消息
    const zhTWResponse = await fetch(chrome.runtime.getURL('_locales/zh_TW/messages.json'));
    messagesCache.zh_TW = await zhTWResponse.json();

    // 加载完成后，获取当前语言设置
    chrome.storage.sync.get(['language'], (result) => {
      const storedLanguage = result.language || 'auto';
      let displayLanguage = storedLanguage;

      // 如果是自动设置，则使用浏览器语言
      if (storedLanguage === 'auto') {
        const browserLang = chrome.i18n.getUILanguage();
        // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
        const rawNavigatorLanguage = navigator.language || navigator.userLanguage;

        console.log('浏览器UI语言:', browserLang, '原始浏览器语言:', rawNavigatorLanguage);

        // 更严格的语言检测逻辑 - 优先使用原始浏览器语言
        if (rawNavigatorLanguage.startsWith('zh')) {
          if (rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
            displayLanguage = 'zh_TW';
          } else {
            displayLanguage = 'zh_CN';
          }
        } else if (browserLang.startsWith('zh')) {
          // 如果原始浏览器语言不是中文，但Chrome API返回的是中文，再检查Chrome API的结果
          if (browserLang.includes('TW') || browserLang.includes('HK') || browserLang.includes('MO')) {
            displayLanguage = 'zh_TW';
          } else {
            displayLanguage = 'zh_CN';
          }
        } else {
          // 如果不是中文，强制使用英文
          displayLanguage = 'en';
        }
      }

      // 如果浏览器语言不是中文，但显示语言被设置为中文，强制使用英文
      const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
      if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh') && storedLanguage === 'auto') {
        console.log('检测到不一致，强制使用英语。当前设置:', displayLanguage, '浏览器语言:', rawNavigatorLanguage);
        displayLanguage = 'en';
      }

      // 记录最终使用的语言
      console.log('最终使用的语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage, '存储的语言设置:', storedLanguage);

      currentDisplayLanguage = displayLanguage;
      console.log('Content script i18n initialized with language:', currentDisplayLanguage);

      // 标记消息已加载
      messagesLoaded = true;
    });
  } catch (error) {
    console.error('Error loading messages in content script:', error);
  }
}

// 在页面加载时预加载消息，但确保只加载一次
async function ensureMessagesLoaded() {
  if (!messagesLoaded) {
    await loadMessages();
  }
}

// 立即加载消息文件
loadMessages();

// 监听语言设置变化
chrome.storage.onChanged.addListener(function(changes, areaName) {
  if (areaName === 'sync' && changes.language) {
    console.log('语言设置已变更，重新加载消息');
    loadMessages();

    // 延迟更新浮动按钮
    setTimeout(() => {
      const floatingButton = document.getElementById('smartcopy-floating-btn');
      if (floatingButton) {
        // 更新按钮文本
        floatingButton.innerHTML = getMessage('floating_button_text');

        // 根据按钮当前状态更新标题
        const computedStyle = window.getComputedStyle(floatingButton);
        const bgColor = computedStyle.backgroundColor;
        const isCurrentlyEnabled = bgColor.includes('66, 133, 244') || bgColor.includes('rgb(66, 133, 244)');

        if (isCurrentlyEnabled) {
          floatingButton.title = getMessage('floating_button_enabled');
        } else {
          floatingButton.title = getMessage('floating_button_disabled');
        }

        console.log('浮动按钮已更新为新语言:', currentDisplayLanguage);
      }
    }, 200);
  }
});

/**
 * 获取国际化消息
 * @param {string} messageKey - 消息键
 * @param {Array|string} substitutions - 替换参数，可以是字符串或字符串数组
 * @returns {string} 国际化消息
 */
function getMessage(messageKey, substitutions) {
  // 尝试确保消息已加载
  if (!messagesLoaded) {
    // 不显示警告，直接使用Chrome的i18n API
    return chrome.i18n.getMessage(messageKey, substitutions) || messageKey;
  }

  let message = '';
  let messageSource = '';

  // 根据当前显示语言获取消息
  if (currentDisplayLanguage === 'en') {
    // 尝试从缓存中获取英文消息
    if (messagesCache.en[messageKey] && messagesCache.en[messageKey].message) {
      message = messagesCache.en[messageKey].message;
      messageSource = 'en-cache';
    }
  } else if (currentDisplayLanguage === 'zh_CN') {
    // 尝试从缓存中获取简体中文消息
    if (messagesCache.zh_CN[messageKey] && messagesCache.zh_CN[messageKey].message) {
      message = messagesCache.zh_CN[messageKey].message;
      messageSource = 'zh_CN-cache';
    }
  } else if (currentDisplayLanguage === 'zh_TW') {
    // 尝试从缓存中获取繁体中文消息
    if (messagesCache.zh_TW[messageKey] && messagesCache.zh_TW[messageKey].message) {
      message = messagesCache.zh_TW[messageKey].message;
      messageSource = 'zh_TW-cache';
    }
  }

  // 如果从缓存中没有找到消息，尝试使用Chrome的i18n API
  if (!message) {
    message = chrome.i18n.getMessage(messageKey, substitutions);
    messageSource = 'chrome-i18n';
  } else if (substitutions) {
    // 如果有替换参数，手动进行替换
    if (Array.isArray(substitutions)) {
      // 如果是数组，按顺序替换 $1, $2, ...
      substitutions.forEach((substitution, index) => {
        const placeholder = `$${index + 1}`;
        message = message.replace(new RegExp(placeholder, 'g'), substitution);
      });
    } else if (typeof substitutions === 'string') {
      // 如果是字符串，替换 $1
      message = message.replace(/\$1/g, substitutions);
    }
  }

  // 调试日志 - 只在开发模式下显示
  if (!message) {
    // 只有在找不到消息时才显示警告
    console.warn(`No message found for key: ${messageKey} (${currentDisplayLanguage})`);
  }

  // 如果消息为空或未定义，返回消息键
  return message || messageKey;
}

// 导出 getMessage 函数，以便在 content.js 中使用
window.contentGetMessage = getMessage;
