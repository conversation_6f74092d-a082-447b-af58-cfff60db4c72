// 弹出页面脚本
import { getSupportedFormats, formatTabInfo, formatTabsAsHtmlTable, formatTabsAsJson } from './utils.js';

// 使用全局的 getMessage 函数，创建一个辅助函数来确保使用全局函数
function getI18nMessage(key) {
  return window.getMessage ? window.getMessage(key) : key;
}
// 获取当前活动tab的标准化域名
async function getCurrentTabDomain() {
  return new Promise((resolve) => {
    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
      if (tabs.length === 0) return resolve(null);
      try {
        const url = new URL(tabs[0].url || '');
        const host = url.hostname;
        const parts = host.split('.');
        if (parts.length > 2) {
          resolve(parts.slice(-2).join('.'));
        } else {
          resolve(host);
        }
      } catch (e) {
        resolve(null);
      }
    });
  });
}

// DOM元素
const formatSelect = document.getElementById('format-select');
const copyCurrentTabBtn = document.getElementById('copy-current-tab');
const copyAllTabsBtn = document.getElementById('copy-all-tabs');
const clipboardHistoryBtn = document.getElementById('clipboard-history');
const autoCopyToggle = document.getElementById('auto-copy-toggle');
const superCopyToggle = document.getElementById('super-copy-toggle');
// 调试模式开关已移除
const openOptionsBtn = document.getElementById('open-options');

// 状态提示元素
let statusElement = null;

// 初始化
async function initialize() {
  // 清空现有选项
  formatSelect.innerHTML = '';

  // 定义格式选项
  const formats = [
    { id: 'default', nameKey: 'format_default' },
    { id: 'title-only', nameKey: 'format_title_only' },
    { id: 'url-only', nameKey: 'format_url_only' },
    { id: 'title-dash-url', nameKey: 'format_title_dash_url' },
    { id: 'markdown', nameKey: 'format_markdown' },
    { id: 'html', nameKey: 'format_html' },
    { id: 'csv', nameKey: 'format_csv' },
    { id: 'json', nameKey: 'format_json' },
    { id: 'html-table', nameKey: 'format_html_table' }
  ];

  // 为默认格式选择器添加选项
  formats.forEach(format => {
    const option = document.createElement('option');
    option.value = format.id;
    // 使用辅助函数获取国际化消息
    option.textContent = getI18nMessage(format.nameKey);
    formatSelect.appendChild(option);
  });

  // 加载保存的设置
  await loadSettings();

  // 添加事件监听器
  formatSelect.addEventListener('change', handleFormatChange);

  // 主按钮点击事件 - 使用默认格式
  copyCurrentTabBtn.addEventListener('click', () => {
    handleCopyCurrentTab();
  });

  copyAllTabsBtn.addEventListener('click', () => {
    handleCopyAllTabs();
  });

  clipboardHistoryBtn.addEventListener('click', handleOpenClipboardHistory);

  // 自动复制开关事件监听器
  if (autoCopyToggle) {
    autoCopyToggle.addEventListener('change', handleAutoCopyToggle);
  }

  // 超级复制开关事件监听器
  if (superCopyToggle) {
    superCopyToggle.addEventListener('change', handleSuperCopyToggle);
  }

  // 调试模式开关已移除

  if (openOptionsBtn) {
    openOptionsBtn.addEventListener('click', handleOpenOptions);
  }

  // 监听来自选项页面的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'settingsUpdated') {
      updateUIFromSettings(message.settings);
      sendResponse({ success: true });
    }
  });
}

// 调试模式开关已移除，只在开发环境中使用

// 加载设置，只加载当前网站的超级复制设置
async function loadSettings() {
  const settings = await chrome.storage.sync.get(['copyFormat', 'autoTextCopy', 'superCopySiteSettings']);
  const domain = await getCurrentTabDomain();

  // 获取当前网站的超级复制设置
  let superCopyEnabled = false; // 默认不启用

  // 调试模式开关已移除

  if (domain && settings.superCopySiteSettings && typeof settings.superCopySiteSettings[domain] === 'boolean') {
    superCopyEnabled = settings.superCopySiteSettings[domain];
    console.log(`当前网站 ${domain} 的超级复制设置为: ${superCopyEnabled}`);
  } else {
    console.log(`当前网站 ${domain} 没有超级复制设置，默认不启用`);
  }

  // 更新UI显示
  updateUIFromSettings({ ...settings, superCopy: superCopyEnabled });

  // 如果没有域名，禁用超级复制开关
  if (!domain && superCopyToggle) {
    superCopyToggle.disabled = true;
    // 添加提示信息
    const container = superCopyToggle.closest('.auto-copy-container');
    if (container) {
      const label = container.querySelector('.auto-copy-label');
      if (label) {
        label.textContent = getI18nMessage('super_copy_unavailable');
      }
    }
  }
}

// 从设置更新UI
function updateUIFromSettings(settings) {
  // 设置选中的格式
  if (settings.copyFormat) {
    formatSelect.value = settings.copyFormat;
  }

  // 设置自动复制开关状态
  if (autoCopyToggle && settings.hasOwnProperty('autoTextCopy')) {
    autoCopyToggle.checked = settings.autoTextCopy !== false;
  }

  // 设置超级复制开关状态，默认全局不启用
  if (superCopyToggle) {
    superCopyToggle.checked = settings.superCopy === true;
  }
}

// 处理格式变更
async function handleFormatChange() {
  const format = formatSelect.value;
  try {
    // 保存到存储
    await chrome.storage.sync.set({ copyFormat: format });

    // 通知设置页面更新
    chrome.runtime.sendMessage({
      action: 'settingsUpdated',
      settings: { copyFormat: format }
    }).catch(error => {
      // 忽略消息发送失败的情况（设置页面可能未打开）
      console.log('通知设置页面更新格式失败，可能设置页面未打开');
    });

    console.log('复制格式已更新:', format);
  } catch (error) {
    console.error('保存复制格式失败:', error);
  }
}

// 处理复制当前标签页
async function handleCopyCurrentTab() {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab) {
      showStatus(getI18nMessage('cannot_get_current_tab'), 'error');
      return;
    }

    // 获取选中的格式
    const format = formatSelect.value;

    // 使用utils.js中的formatTabInfo函数格式化标签页信息
    const formattedText = formatTabInfo(tab, format);

    // 复制到剪贴板
    await copyToClipboard(formattedText);

    // 保存到剪贴板历史
    await saveToClipboardHistory(formattedText, format);

    // 显示成功消息
    showStatus(getI18nMessage('copied_to_clipboard'));

    // 添加按钮动画效果
    animateButton(copyCurrentTabBtn);

  } catch (error) {
    console.error('复制当前标签页失败:', error);
    showStatus(getI18nMessage('copy_failed').replace('$ERROR$', error.message), 'error');
  }
}

// 处理复制所有标签页
async function handleCopyAllTabs() {
  try {
    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    if (!tabs || tabs.length === 0) {
      showStatus(getI18nMessage('no_tabs_found'), 'error');
      return;
    }

    // 获取选中的格式
    const format = formatSelect.value;

    let combinedText = '';

    // 特殊格式处理
    if (format === 'html-table') {
      combinedText = formatTabsAsHtmlTable(tabs);
    } else if (format === 'json') {
      combinedText = formatTabsAsJson(tabs);
    } else {
      // 普通格式，每个标签页一行
      const formattedTabs = tabs.map(tab => formatTabInfo(tab, format));
      combinedText = formattedTabs.join('\n\n');
    }

    // 复制到剪贴板
    await copyToClipboard(combinedText);

    // 保存到剪贴板历史
    await saveToClipboardHistory(combinedText, format);

    // 显示成功消息
    showStatus(getI18nMessage('copied_to_clipboard'));

    // 添加按钮动画效果
    animateButton(copyAllTabsBtn);

  } catch (error) {
    console.error('复制所有标签页失败:', error);
    showStatus(getI18nMessage('copy_failed').replace('$ERROR$', error.message), 'error');
  }
}

// 处理自动复制开关
async function handleAutoCopyToggle() {
  const isChecked = autoCopyToggle.checked;
  console.log('自动复制开关状态变更:', isChecked);

  try {
    // 保存设置到存储
    await chrome.storage.sync.set({ autoTextCopy: isChecked });

    // 向背景脚本发送消息，由背景脚本广播到所有页面
    await chrome.runtime.sendMessage({
      action: 'settingsUpdated',
      settings: { autoTextCopy: isChecked }
    });

    // 通知所有标签页更新设置
    const tabs = await chrome.tabs.query({});
    for (const tab of tabs) {
      chrome.tabs.sendMessage(tab.id, {
        action: 'updateSettings',
        settings: { autoTextCopy: isChecked },
        showNotification: true,  // 添加标志，指示需要显示通知
        notificationMessage: isChecked ? getI18nMessage('auto_copy_enabled') : getI18nMessage('auto_copy_disabled')  // 添加通知消息
      }).catch(() => {
        // 忽略无法发送消息的标签页（如chrome://页面）
      });
    }

  } catch (err) {
    console.error('发送消息失败:', err);
    // 不需要在这里显示错误通知，因为文本已经复制成功
  }
}

// 处理超级复制开关（只影响当前域名，不影响全局设置）
async function handleSuperCopyToggle() {
  const isChecked = superCopyToggle.checked;
  const domain = await getCurrentTabDomain();
  console.log('超级复制开关状态变更:', isChecked, '域名:', domain);

  if (!domain) {
    showStatus(getI18nMessage('copy_failed').replace('$ERROR$', 'Cannot get domain'), 'error');
    return;
  }

  try {
    // 只修改站点级别设置，不改变全局设置
    const settings = await chrome.storage.sync.get(['superCopySiteSettings']);
    const siteSettings = settings.superCopySiteSettings || {};

    // 更新当前网站的设置
    siteSettings[domain] = isChecked;
    await chrome.storage.sync.set({ superCopySiteSettings: siteSettings });

    // 通知当前tab的content script刷新状态
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tab && tab.id) {
      chrome.tabs.sendMessage(tab.id, {
        action: 'updateSiteSettings',
        domain: domain,
        enabled: isChecked
      }).catch(() => {});
    }

    // 不再显示弹出窗口中的通知，所有通知都由内容脚本中的红色浮动通知显示
    // showStatus(`已${isChecked ? '启用' : '禁用'}当前网站的超级复制功能`);
  } catch (err) {
    console.error('更新站点设置失败:', err);
    // 不再显示弹出窗口中的通知
    // showStatus('更新设置失败', 'error');
  }
}

// 处理打开选项页面
function handleOpenOptions() {
  chrome.runtime.openOptionsPage();
}

// 处理打开剪贴板历史记录页面
function handleOpenClipboardHistory() {
  // 打开剪贴板历史记录页面
  chrome.tabs.create({ url: 'clipboard-history.html' });

  // 添加按钮动画效果
  animateButton(clipboardHistoryBtn);

  // 关闭弹出窗口
  window.close();
}

// 按钮点击动画
function animateButton(button) {
  button.classList.add('clicked');
  setTimeout(() => {
    button.classList.remove('clicked');
  }, 200);
}

// 复制文本到剪贴板
async function copyToClipboard(text) {
  if (!text) {
    throw new Error(getI18nMessage('no_text_to_copy'));
  }

  // 保留所有换行符，不做处理
  // 确保换行符在所有环境下都能正确显示

  try {
    // 尝试使用现代的 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      return true;
    }

    // 如果现代API不可用，使用传统方法
    // 创建文本区域元素
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 确保文本区域不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = 0;
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    textArea.style.whiteSpace = 'pre'; // 保留换行符
    document.body.appendChild(textArea);

    // 选择文本并复制
    textArea.focus();
    textArea.select();

    const success = document.execCommand('copy');
    document.body.removeChild(textArea);

    if (success) {
      return true;
    }

    throw new Error('复制失败');
  } catch (error) {
    console.error('复制到剪贴板失败:', error);

    // 尝试通过发送消息到内容脚本来复制
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab) {
        await new Promise((resolve, reject) => {
          chrome.tabs.sendMessage(tab.id, {
            action: 'copyToClipboard',
            text: text
          }, response => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else if (response && response.success) {
              resolve(true);
            } else {
              reject(new Error('内容脚本复制失败'));
            }
          });
        });
        return true;
      }
    } catch (backupError) {
      console.error('备用复制方法也失败:', backupError);
      throw error; // 抛出原始错误
    }
  }
}

// 显示状态消息
function showStatus(message, type = 'success') {
  // 如果状态元素不存在，创建一个
  if (!statusElement) {
    statusElement = document.createElement('div');
    statusElement.className = 'status-message';
    document.body.appendChild(statusElement);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .status-message {
        position: fixed;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
        z-index: 1000;
        opacity: 0;
        display: flex;
        align-items: center;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      .status-message::before {
        margin-right: 8px;
        font-size: 16px;
      }
      .status-message.success {
        background-color: rgba(26, 115, 232, 0.95);
        color: white;
      }
      .status-message.success::before {
        content: "✓";
      }
      .status-message.error {
        background-color: rgba(217, 48, 37, 0.95);
        color: white;
      }
      .status-message.error::before {
        content: "✗";
      }
      .status-message.warning {
        background-color: rgba(251, 188, 5, 0.95);
        color: #202124;
      }
      .status-message.warning::before {
        content: "⚠";
      }
      .status-message.info {
        background-color: rgba(95, 99, 104, 0.95);
        color: white;
      }
      .status-message.info::before {
        content: "ℹ";
      }
      .status-message.visible {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
    `;
    document.head.appendChild(style);
  }

  // 设置消息内容和类型
  statusElement.textContent = message;
  statusElement.className = `status-message ${type}`;

  // 显示消息
  setTimeout(() => {
    statusElement.classList.add('visible');
  }, 10);

  // 2.5秒后隐藏消息
  setTimeout(() => {
    statusElement.classList.remove('visible');
  }, 2500);
}

// 保存到剪贴板历史
async function saveToClipboardHistory(text, format) {
  try {
    // 获取剪贴板历史管理器
    const clipboardHistory = await getClipboardHistory();

    // 添加到历史记录
    await clipboardHistory.addItem({
      text,
      format,
      source: 'tab',
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('保存到剪贴板历史失败:', error);
  }
}

// 获取剪贴板历史管理器
async function getClipboardHistory() {
  if (!window.clipboardHistory) {
    // 导入剪贴板历史记录单例实例
    const { clipboardHistory } = await import('./clipboard-history.js');
    window.clipboardHistory = clipboardHistory;
  }
  return window.clipboardHistory;
}

// 初始化弹出页面
document.addEventListener('DOMContentLoaded', initialize);

// 将 initialize 函数暴露为全局函数，以便 i18n.js 可以调用
window.initialize = initialize;
