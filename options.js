// 设置页面脚本
import { getSupportedFormats } from './utils.js';
import { clipboardHistory } from './clipboard-history.js';

// 使用全局的 getMessage 函数，创建一个辅助函数来确保使用全局函数
function getI18nMessage(key) {
  return window.getMessage ? window.getMessage(key) : key;
}

// DOM元素
const copyFormatSelect = document.getElementById('copy-format');
const autoCopyToggle = document.getElementById('auto-copy-toggle');
const minCharactersInput = document.getElementById('min-characters');
const copyTextFormatSelect = document.getElementById('copy-text-format');
const maxHistoryItemsInput = document.getElementById('max-history-items');
const languageSelect = document.getElementById('language-select');
const showFloatingButtonToggle = document.getElementById('show-floating-button-toggle');
const saveBtn = document.getElementById('save-btn');
const resetBtn = document.getElementById('reset-btn');
const statusMessage = document.getElementById('status-message');

// 默认设置
const DEFAULT_SETTINGS = {
  copyFormat: 'default', // [标题] URL
  autoTextCopy: true,
  minCharacters: 5,
  copyTextFormat: 'plain', // 默认为纯文本
  maxHistoryItems: 10,
  language: 'auto', // 自动检测语言
  showFloatingButton: false // 默认不显示浮动按钮
};

// 初始化
async function initialize() {
  try {
    // 加载格式选项
    addCopyFormatOptions();

    // 加载设置
    await loadSettings();

    // 添加事件监听器
    saveBtn.addEventListener('click', saveSettings);
    resetBtn.addEventListener('click', resetSettings);
    autoCopyToggle.addEventListener('change', handleAutoCopyToggle);
    showFloatingButtonToggle.addEventListener('change', handleShowFloatingButtonToggle);

    // 添加复制格式选择器的change事件监听器，实现实时同步
    copyFormatSelect.addEventListener('change', handleCopyFormatChange);

    // 添加选中文字复制格式选择器的change事件监听器
    copyTextFormatSelect.addEventListener('change', handleCopyTextFormatChange);

    // 添加最小字符数输入框的change和input事件监听器
    minCharactersInput.addEventListener('change', handleMinCharactersChange);
    minCharactersInput.addEventListener('input', handleMinCharactersChange);

    // 添加最大历史记录数量输入框的change事件监听器（仅在失去焦点时触发，减少写入频率）
    maxHistoryItemsInput.addEventListener('change', handleMaxHistoryItemsChange);
    // 移除input事件监听器，避免频繁触发存储操作
    // maxHistoryItemsInput.addEventListener('input', handleMaxHistoryItemsChange);

    // 添加语言选择器的change事件监听器
    if (languageSelect) {
      // 添加语言选项
      addLanguageOptions();

      // 从 Chrome 存储中获取语言设置
      chrome.storage.sync.get(['language'], (result) => {
        const storedLanguage = result.language || 'auto';
        console.log('Found language in storage:', storedLanguage);
        languageSelect.value = storedLanguage;

        // 强制应用选择的语言
        if (storedLanguage !== 'auto') {
          document.documentElement.setAttribute('lang', storedLanguage);
        }
      });

      languageSelect.addEventListener('change', handleLanguageChange);
    }

    // 监听设置更新消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'settingsUpdated') {
        console.log('选项页面收到设置更新消息:', message.settings);
        updateUIFromSettings(message.settings);
        sendResponse({ success: true });
      }
    });

    // 页面加载完成后，再次检查复制格式设置
    setTimeout(async () => {
      try {
        // 检查标签页复制格式设置
        const copyFormatSettings = await chrome.storage.sync.get(['copyFormat']);
        console.log('页面加载完成后再次检查标签页复制格式设置:', copyFormatSettings.copyFormat);

        if (copyFormatSettings.copyFormat && copyFormatSelect.value !== copyFormatSettings.copyFormat) {
          console.warn(getI18nMessage('tab_format_mismatch'));
          copyFormatSelect.value = copyFormatSettings.copyFormat;

          // 强制保存设置
          await chrome.storage.sync.set({ copyFormat: copyFormatSettings.copyFormat });
          console.log('已强制保存标签页复制格式设置:', copyFormatSettings.copyFormat);
        }

        // 检查选中文字复制格式设置
        const copyTextFormatSettings = await chrome.storage.sync.get(['copyTextFormat']);
        console.log('页面加载完成后再次检查选中文字复制格式设置:', copyTextFormatSettings.copyTextFormat);

        if (copyTextFormatSettings.copyTextFormat && copyTextFormatSelect.value !== copyTextFormatSettings.copyTextFormat) {
          console.warn(getI18nMessage('text_format_mismatch'));
          copyTextFormatSelect.value = copyTextFormatSettings.copyTextFormat;

          // 强制保存设置
          await chrome.storage.sync.set({ copyTextFormat: copyTextFormatSettings.copyTextFormat });
          console.log('已强制保存选中文字复制格式设置:', copyTextFormatSettings.copyTextFormat);
        }
      } catch (error) {
        console.error('页面加载完成后检查复制格式设置失败:', error);
      }
    }, 1000);

    console.log('选项页面初始化完成');
  } catch (error) {
    console.error('初始化失败:', error);
    showStatus(getI18nMessage('initialization_failed') || '初始化失败，请刷新页面重试', 'error');
  }
}

// 加载设置
async function loadSettings() {
  try {
    console.log('开始加载设置...');
    const settings = await chrome.storage.sync.get(DEFAULT_SETTINGS);
    console.log('加载到的设置:', settings);

    // 特别检查 copyFormat 设置
    if (settings.copyFormat) {
      console.log('检测到标签页复制格式设置:', settings.copyFormat);
    } else {
      console.warn('未检测到标签页复制格式设置，使用默认值:', DEFAULT_SETTINGS.copyFormat);
      settings.copyFormat = DEFAULT_SETTINGS.copyFormat;

      // 保存默认值到存储
      await chrome.storage.sync.set({ copyFormat: DEFAULT_SETTINGS.copyFormat });

      // 验证设置是否成功保存
      const verifySettings = await chrome.storage.sync.get(['copyFormat']);
      console.log('验证保存的默认标签页复制格式:', verifySettings.copyFormat);

      // 确保使用实际保存的值
      settings.copyFormat = verifySettings.copyFormat;
    }

    // 特别检查 copyTextFormat 设置
    if (settings.copyTextFormat) {
      console.log('检测到选中文字复制格式设置:', settings.copyTextFormat);
    } else {
      console.warn('未检测到选中文字复制格式设置，使用默认值:', DEFAULT_SETTINGS.copyTextFormat);
      settings.copyTextFormat = DEFAULT_SETTINGS.copyTextFormat;

      // 保存默认值到存储
      await chrome.storage.sync.set({ copyTextFormat: DEFAULT_SETTINGS.copyTextFormat });

      // 验证设置是否成功保存
      const verifySettings = await chrome.storage.sync.get(['copyTextFormat']);
      console.log('验证保存的默认选中文字复制格式:', verifySettings.copyTextFormat);

      // 确保使用实际保存的值
      settings.copyTextFormat = verifySettings.copyTextFormat;
    }

    // 确保UI更新前记录最终使用的设置值
    console.log('最终使用的标签页复制格式设置:', settings.copyFormat);
    console.log('最终使用的选中文字复制格式设置:', settings.copyTextFormat);

    updateUIFromSettings(settings);

    // 验证UI是否正确更新
    if (copyFormatSelect) {
      console.log('UI中选中的标签页复制格式:', copyFormatSelect.value);
      if (copyFormatSelect.value !== settings.copyFormat) {
        console.warn('UI中的标签页复制格式值与设置不匹配，强制更新UI');
        copyFormatSelect.value = settings.copyFormat;
      }
    }

    if (copyTextFormatSelect) {
      console.log('UI中选中的文字复制格式:', copyTextFormatSelect.value);
      if (copyTextFormatSelect.value !== settings.copyTextFormat) {
        console.warn('UI中的选中文字复制格式值与设置不匹配，强制更新UI');
        copyTextFormatSelect.value = settings.copyTextFormat;
      }
    }
  } catch (error) {
    console.error('加载设置时出错:', error);
  }
}

// 从设置更新UI
function updateUIFromSettings(settings) {
  console.log('从设置更新UI:', settings);
  // 只更新存在的设置项
  if (settings.copyFormat !== undefined) {
    console.log('更新标签页复制格式UI为:', settings.copyFormat);
    copyFormatSelect.value = settings.copyFormat;

    // 确保选择器的值已正确设置
    if (copyFormatSelect.value !== settings.copyFormat) {
      console.warn('标签页复制格式选择器值设置失败，尝试强制设置');

      // 检查选项是否存在
      let optionExists = false;
      for (const option of copyFormatSelect.options) {
        if (option.value === settings.copyFormat) {
          optionExists = true;
          break;
        }
      }

      if (!optionExists) {
        console.warn(`选项 ${settings.copyFormat} 不存在，添加该选项`);
        const newOption = document.createElement('option');
        newOption.value = settings.copyFormat;
        newOption.textContent = settings.copyFormat;
        copyFormatSelect.appendChild(newOption);
      }

      // 再次尝试设置值
      copyFormatSelect.value = settings.copyFormat;
    }

    // 强制保存设置到存储，确保设置被持久化
    chrome.storage.sync.set({ copyFormat: settings.copyFormat })
      .then(() => {
        console.log('已强制保存标签页复制格式设置:', settings.copyFormat);

        // 验证设置是否成功保存
        return chrome.storage.sync.get(['copyFormat']);
      })
      .then((result) => {
        console.log('验证强制保存的标签页复制格式:', result.copyFormat);
      })
      .catch(error => {
        console.error('强制保存标签页复制格式设置失败:', error);
      });
  }

  if (settings.autoTextCopy !== undefined) {
    autoCopyToggle.checked = settings.autoTextCopy;
  }

  if (settings.minCharacters !== undefined) {
    minCharactersInput.value = settings.minCharacters;
  }

  if (settings.copyTextFormat !== undefined) {
    console.log('更新选中文字复制格式UI为:', settings.copyTextFormat);
    copyTextFormatSelect.value = settings.copyTextFormat;

    // 确保选择器的值已正确设置
    if (copyTextFormatSelect.value !== settings.copyTextFormat) {
      console.warn('选中文字复制格式选择器值设置失败，尝试强制设置');

      // 检查选项是否存在
      let optionExists = false;
      for (const option of copyTextFormatSelect.options) {
        if (option.value === settings.copyTextFormat) {
          optionExists = true;
          break;
        }
      }

      if (!optionExists) {
        console.warn(`选项 ${settings.copyTextFormat} 不存在，添加该选项`);
        const newOption = document.createElement('option');
        newOption.value = settings.copyTextFormat;
        newOption.textContent = settings.copyTextFormat === 'plain' ? '纯文本' : 'Markdown';
        copyTextFormatSelect.appendChild(newOption);
      }

      // 再次尝试设置值
      copyTextFormatSelect.value = settings.copyTextFormat;
    }

    // 强制保存设置到存储，确保设置被持久化
    chrome.storage.sync.set({ copyTextFormat: settings.copyTextFormat })
      .then(() => {
        console.log('已强制保存选中文字复制格式设置:', settings.copyTextFormat);

        // 验证设置是否成功保存
        return chrome.storage.sync.get(['copyTextFormat']);
      })
      .then((result) => {
        console.log('验证强制保存的选中文字复制格式:', result.copyTextFormat);
      })
      .catch(error => {
        console.error('强制保存选中文字复制格式设置失败:', error);
      });
  }

  if (settings.maxHistoryItems !== undefined) {
    maxHistoryItemsInput.value = settings.maxHistoryItems;
  }

  if (settings.language !== undefined && languageSelect) {
    languageSelect.value = settings.language;
  }

  if (settings.showFloatingButton !== undefined && showFloatingButtonToggle) {
    showFloatingButtonToggle.checked = settings.showFloatingButton;
  }

  console.log('UI已从设置更新:', settings);
}

// 保存设置
async function saveSettings() {
  try {
    const settings = {
      copyFormat: copyFormatSelect.value,
      autoTextCopy: autoCopyToggle.checked,
      minCharacters: parseInt(minCharactersInput.value, 10) || DEFAULT_SETTINGS.minCharacters,
      copyTextFormat: copyTextFormatSelect.value,
      maxHistoryItems: parseInt(maxHistoryItemsInput.value, 10) || DEFAULT_SETTINGS.maxHistoryItems,
      language: languageSelect ? languageSelect.value : DEFAULT_SETTINGS.language,
      showFloatingButton: showFloatingButtonToggle ? showFloatingButtonToggle.checked : DEFAULT_SETTINGS.showFloatingButton
    };

    console.log('准备保存设置:', settings);

    // 特别处理选中文字复制格式设置
    console.log('特别保存选中文字复制格式设置:', settings.copyTextFormat);
    await chrome.storage.sync.set({ copyTextFormat: settings.copyTextFormat });

    // 验证选中文字复制格式设置是否保存成功
    const verifyTextFormat = await chrome.storage.sync.get(['copyTextFormat']);
    console.log('验证选中文字复制格式设置:', verifyTextFormat.copyTextFormat);

    if (verifyTextFormat.copyTextFormat !== settings.copyTextFormat) {
      console.warn('选中文字复制格式设置保存失败，重试...');
      await chrome.storage.sync.set({ copyTextFormat: settings.copyTextFormat });
    }

    // 分开保存其他设置项，避免整体保存可能导致的问题
    for (const [key, value] of Object.entries(settings)) {
      if (key !== 'copyTextFormat') { // 跳过已保存的选中文字复制格式设置
        await chrome.storage.sync.set({ [key]: value });
        console.log(`已保存设置项: ${key} = ${value}`);
      }
    }

    // 确认设置已正确保存
    const savedSettings = await chrome.storage.sync.get(null);
    console.log('所有保存的设置:', savedSettings);

    // 更新剪贴板历史记录最大数量
    await clipboardHistory.updateMaxItems(settings.maxHistoryItems);

    showStatus(getI18nMessage('settings_saved'), 'success');

    // 通知所有标签页更新设置
    const tabs = await chrome.tabs.query({});
    for (const tab of tabs) {
      chrome.tabs.sendMessage(tab.id, {
        action: 'updateSettings',
        settings: {
          autoTextCopy: settings.autoTextCopy,
          minCharacters: settings.minCharacters,
          copyTextFormat: settings.copyTextFormat,
          maxHistoryItems: settings.maxHistoryItems,
          showFloatingButton: settings.showFloatingButton
        }
      }).catch(() => {
        // 忽略无法发送消息的标签页（如chrome://页面）
      });
    }

    // 通知弹出窗口更新设置
    chrome.runtime.sendMessage({
      action: 'settingsUpdated',
      settings: settings
    }).catch(() => {
      // 忽略消息发送失败的情况
    });

  } catch (error) {
    console.error('保存设置失败:', error);
    showStatus(getMessage('settings_save_failed'), 'error');
  }
}

// 处理自动复制开关
function handleAutoCopyToggle() {
  const isChecked = autoCopyToggle.checked;
  console.log('自动复制开关状态变更:', isChecked);

  // 立即更新存储
  chrome.storage.sync.set({ autoTextCopy: isChecked })
    .then(() => {
      // 直接向背景脚本发送消息，由背景脚本广播到所有页面
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { autoTextCopy: isChecked }
      }).catch(error => {
        console.error('发送设置更新消息失败:', error);
      });

      showStatus(isChecked ? getI18nMessage('auto_copy_enabled') : getI18nMessage('auto_copy_disabled'), 'success');
    })
    .catch(error => {
      console.error('保存自动复制设置失败:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 处理浮动按钮显示开关
function handleShowFloatingButtonToggle() {
  const isChecked = showFloatingButtonToggle.checked;
  console.log('浮动按钮显示开关状态变更:', isChecked);

  // 立即更新存储
  chrome.storage.sync.set({ showFloatingButton: isChecked })
    .then(() => {
      // 直接向背景脚本发送消息，由背景脚本广播到所有页面
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { showFloatingButton: isChecked }
      }).catch(error => {
        console.error('发送设置更新消息失败:', error);
      });

      showStatus(isChecked ? getI18nMessage('floating_button_enabled') : getI18nMessage('floating_button_disabled'), 'success');
    })
    .catch(error => {
      console.error('保存浮动按钮显示设置失败:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 处理最小字符数变更
function handleMinCharactersChange() {
  // 获取输入值并确保是有效的整数
  let value = parseInt(minCharactersInput.value, 10);

  // 验证值的有效性
  if (isNaN(value) || value < 1) {
    value = DEFAULT_SETTINGS.minCharacters;
    minCharactersInput.value = value;
  }

  console.log('最小字符数变更:', value);

  // 立即更新存储
  chrome.storage.sync.set({ minCharacters: value })
    .then(() => {
      // 通知背景脚本更新设置
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { minCharacters: value }
      }).catch(error => {
        console.error('发送设置更新消息失败:', error);
      });

      // 使用国际化消息
      const messageTemplate = getI18nMessage('min_characters_updated');
      showStatus(messageTemplate.replace('{value}', value), 'success');
    })
    .catch(error => {
      console.error('保存最小字符数设置失败:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 处理最大历史记录数量变更
let maxHistoryItemsDebounceTimer = null;
function handleMaxHistoryItemsChange() {
  // 获取输入值并确保是有效的整数
  let value = parseInt(maxHistoryItemsInput.value, 10);

  // 验证值的有效性
  if (isNaN(value) || value < 1) {
    value = DEFAULT_SETTINGS.maxHistoryItems;
    maxHistoryItemsInput.value = value;
  }

  console.log('最大历史记录数量变更:', value);

  // 使用防抖，避免频繁写入
  if (maxHistoryItemsDebounceTimer) {
    clearTimeout(maxHistoryItemsDebounceTimer);
  }

  maxHistoryItemsDebounceTimer = setTimeout(() => {
    // 立即更新存储
    chrome.storage.sync.set({ maxHistoryItems: value })
      .then(() => {
        // 更新剪贴板历史记录最大数量
        return clipboardHistory.updateMaxItems(value);
      })
      .then(() => {
        // 通知背景脚本更新设置
        chrome.runtime.sendMessage({
          action: 'settingsUpdated',
          settings: { maxHistoryItems: value }
        }).catch(error => {
          console.error('发送设置更新消息失败:', error);
        });

        // 使用国际化消息
        const messageTemplate = getI18nMessage('max_history_updated');
        showStatus(messageTemplate.replace('{value}', value), 'success');
      })
      .catch(error => {
        console.error('保存最大历史记录数量设置失败:', error);
        showStatus(getI18nMessage('settings_save_failed'), 'error');

        // 如果是配额错误，提供更具体的提示
        if (error.message && error.message.includes('MAX_WRITE_OPERATIONS_PER_MINUTE')) {
          showStatus(getI18nMessage('operation_too_frequent'), 'error');
        }
      });
  }, 500); // 500毫秒的防抖延迟
}

// 处理复制格式变更
function handleCopyFormatChange() {
  const selectedFormat = copyFormatSelect.value;
  console.log('复制格式变更:', selectedFormat);

  // 立即更新存储
  chrome.storage.sync.set({ copyFormat: selectedFormat })
    .then(() => {
      // 验证设置是否成功保存
      return chrome.storage.sync.get(['copyFormat']);
    })
    .then((result) => {
      console.log('验证保存的标签页复制格式:', result.copyFormat);

      if (result.copyFormat !== selectedFormat) {
        console.warn('保存的格式与选择的格式不匹配，重新尝试保存');
        return chrome.storage.sync.set({ copyFormat: selectedFormat });
      }

      // 再次验证设置是否成功保存
      return chrome.storage.sync.get(['copyFormat']);
    })
    .then((result) => {
      console.log('二次验证保存的标签页复制格式:', result.copyFormat);

      // 通知背景脚本更新设置
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { copyFormat: result.copyFormat }
      }).catch(error => {
        console.error('发送设置更新消息失败:', error);
      });

      // 使用国际化消息
      const messageTemplate = getI18nMessage('copy_format_updated');
      showStatus(messageTemplate.replace('{format}', result.copyFormat), 'success');
    })
    .catch(error => {
      console.error('保存复制格式设置失败:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 处理选中文字复制格式变更
function handleCopyTextFormatChange() {
  const selectedFormat = copyTextFormatSelect.value;
  console.log('选中文字复制格式变更:', selectedFormat);

  // 立即更新存储
  chrome.storage.sync.set({ copyTextFormat: selectedFormat })
    .then(() => {
      // 验证设置是否成功保存
      return chrome.storage.sync.get(['copyTextFormat']);
    })
    .then((result) => {
      console.log('验证保存的选中文字复制格式:', result.copyTextFormat);

      if (result.copyTextFormat !== selectedFormat) {
        console.warn('保存的格式与选择的格式不匹配，重新尝试保存');
        return chrome.storage.sync.set({ copyTextFormat: selectedFormat });
      }

      // 再次验证设置是否成功保存
      return chrome.storage.sync.get(['copyTextFormat']);
    })
    .then((result) => {
      console.log('二次验证保存的选中文字复制格式:', result.copyTextFormat);

      // 通知所有标签页更新设置
      chrome.tabs.query({}).then(tabs => {
        for (const tab of tabs) {
          chrome.tabs.sendMessage(tab.id, {
            action: 'updateSettings',
            settings: { copyTextFormat: result.copyTextFormat }
          }).catch(() => {
            // 忽略无法发送消息的标签页（如chrome://页面）
          });
        }
      });

      // 通知背景脚本更新设置
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { copyTextFormat: result.copyTextFormat }
      }).catch(error => {
        console.error('发送设置更新消息失败:', error);
      });

      // 使用国际化消息
      const formatKey = result.copyTextFormat === 'plain' ? 'plain_text_format' : 'markdown_format';
      const formatName = getI18nMessage(formatKey);
      const messageTemplate = getI18nMessage('copy_text_format_updated');
      showStatus(messageTemplate.replace('{format}', formatName), 'success');
    })
    .catch(error => {
      console.error('保存选中文字复制格式设置失败:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 重置设置
async function resetSettings() {
  try {
    // 使用自定义对话框，完全控制标题和内容
    const title = getI18nMessage('dialog_title_reset_settings'); // 使用专门为对话框设计的标题
    const message = getI18nMessage('extension_prompt_reset_defaults');
    const confirmText = getI18nMessage('confirm');
    const cancelText = getI18nMessage('cancel');

    let confirmed = false;

    // 检查自定义对话框函数是否可用
    if (typeof window.customConfirmDialog === 'function') {
      // 使用自定义对话框
      confirmed = await window.customConfirmDialog(title, message, confirmText, cancelText);
    } else {
      // 回退到原生confirm
      confirmed = confirm(getI18nMessage('extension_prompt_reset_defaults'));
    }

    if (!confirmed) {
      return;
    }

    console.log('准备重置所有设置为默认值:', DEFAULT_SETTINGS);

    // 分开保存每个默认设置项
    for (const [key, value] of Object.entries(DEFAULT_SETTINGS)) {
      await chrome.storage.sync.set({ [key]: value });
      console.log(`已重置设置项: ${key} = ${value}`);
    }

    // 更新UI
    updateUIFromSettings(DEFAULT_SETTINGS);

    // 确认设置已正确保存
    const savedSettings = await chrome.storage.sync.get(null);
    console.log('重置后的所有设置:', savedSettings);

    // 更新剪贴板历史记录最大数量
    await clipboardHistory.updateMaxItems(DEFAULT_SETTINGS.maxHistoryItems);

    showStatus(getI18nMessage('settings_reset'), 'success');

    // 通知所有标签页更新设置
    const tabs = await chrome.tabs.query({});
    for (const tab of tabs) {
      chrome.tabs.sendMessage(tab.id, {
        action: 'updateSettings',
        settings: DEFAULT_SETTINGS
      }).catch(() => {
        // 忽略无法发送消息的标签页（如chrome://页面）
      });
    }

    // 通知弹出窗口更新设置
    chrome.runtime.sendMessage({
      action: 'settingsUpdated',
      settings: DEFAULT_SETTINGS
    }).catch(() => {
      // 忽略消息发送失败的情况
    });

  } catch (error) {
    console.error('重置设置失败:', error);
    showStatus(getI18nMessage('settings_save_failed'), 'error');
  }
}

// 显示状态消息
function showStatus(message, type = 'success') {
  statusMessage.textContent = message;
  statusMessage.className = `status ${type}`;
  statusMessage.style.opacity = '1';

  // 3秒后淡出
  setTimeout(() => {
    statusMessage.style.opacity = '0';
  }, 3000);
}

// 添加复制格式选项
function addCopyFormatOptions() {
  if (!copyFormatSelect) return;

  // 保存当前选中的值
  const currentCopyFormat = copyFormatSelect.value;
  const currentTextFormat = copyTextFormatSelect ? copyTextFormatSelect.value : null;

  console.log('重新加载格式选项前保存当前值:', {
    copyFormat: currentCopyFormat,
    textFormat: currentTextFormat
  });

  // 清空现有选项
  copyFormatSelect.innerHTML = '';

  // 定义格式选项
  const formats = [
    { id: 'default', nameKey: 'format_default', descKey: 'format_default_desc' },
    { id: 'title-only', nameKey: 'format_title_only', descKey: 'format_title_only_desc' },
    { id: 'url-only', nameKey: 'format_url_only', descKey: 'format_url_only_desc' },
    { id: 'title-dash-url', nameKey: 'format_title_dash_url', descKey: 'format_title_dash_url_desc' },
    { id: 'markdown', nameKey: 'format_markdown', descKey: 'format_markdown_desc' },
    { id: 'html', nameKey: 'format_html', descKey: 'format_html_desc' },
    { id: 'csv', nameKey: 'format_csv', descKey: 'format_csv_desc' },
    { id: 'json', nameKey: 'format_json', descKey: 'format_json_desc' },
    { id: 'html-table', nameKey: 'format_html_table', descKey: 'format_html_table_desc' }
  ];

  // 添加格式选项
  formats.forEach(format => {
    const option = document.createElement('option');
    option.value = format.id;
    // 使用辅助函数获取国际化消息
    const name = getI18nMessage(format.nameKey);
    const desc = getI18nMessage(format.descKey);
    option.textContent = `${name} (${desc})`;
    copyFormatSelect.appendChild(option);
  });

  // 添加选中文字复制格式选项
  if (copyTextFormatSelect) {
    // 清空现有选项
    copyTextFormatSelect.innerHTML = '';

    // 添加选项
    const textFormats = [
      { value: 'plain', messageKey: 'plain_text' },
      { value: 'markdown', messageKey: 'markdown' }
    ];

    textFormats.forEach(format => {
      const option = document.createElement('option');
      option.value = format.value;
      // 使用辅助函数获取国际化消息
      option.textContent = getI18nMessage(format.messageKey);
      copyTextFormatSelect.appendChild(option);
    });
  }

  // 恢复之前选中的值
  if (currentCopyFormat) {
    console.log('恢复标签页复制格式为:', currentCopyFormat);
    copyFormatSelect.value = currentCopyFormat;

    // 如果恢复失败，从存储中获取值
    if (copyFormatSelect.value !== currentCopyFormat) {
      console.warn('恢复标签页复制格式失败，尝试从存储中获取');
      chrome.storage.sync.get(['copyFormat'], function(result) {
        if (result.copyFormat) {
          console.log('从存储中获取到标签页复制格式:', result.copyFormat);
          copyFormatSelect.value = result.copyFormat;
        }
      });
    }
  }

  if (copyTextFormatSelect && currentTextFormat) {
    console.log('恢复选中文字复制格式为:', currentTextFormat);
    copyTextFormatSelect.value = currentTextFormat;

    // 如果恢复失败，从存储中获取值
    if (copyTextFormatSelect.value !== currentTextFormat) {
      console.warn('恢复选中文字复制格式失败，尝试从存储中获取');
      chrome.storage.sync.get(['copyTextFormat'], function(result) {
        if (result.copyTextFormat) {
          console.log('从存储中获取到选中文字复制格式:', result.copyTextFormat);
          copyTextFormatSelect.value = result.copyTextFormat;
        }
      });
    }
  }
}

// 添加语言选项
function addLanguageOptions() {
  if (!languageSelect) return;

  // 清空现有选项
  languageSelect.innerHTML = '';

  // 添加语言选项
  const languages = [
    { value: 'auto', messageKey: 'language_auto' },
    { value: 'en', messageKey: 'language_en' },
    { value: 'zh_CN', messageKey: 'language_zh_CN' },
    { value: 'zh_TW', messageKey: 'language_zh_TW' }
  ];

  languages.forEach(lang => {
    const option = document.createElement('option');
    option.value = lang.value;
    // 使用辅助函数获取国际化消息
    option.textContent = getI18nMessage(lang.messageKey);
    languageSelect.appendChild(option);
  });
}

// 处理语言变更
function handleLanguageChange() {
  const selectedLanguage = languageSelect.value;
  console.log('Language changed by user to:', selectedLanguage);

  // 不需要保存到 localStorage，直接使用 Chrome 存储
  console.log('Changing language to:', selectedLanguage);

  // 立即更新存储
  chrome.storage.sync.set({ language: selectedLanguage })
    .then(() => {
      // 通知背景脚本更新设置
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { language: selectedLanguage }
      }).catch(error => {
        console.error('Failed to send settings update message:', error);
      });

      // 如果有全局的 forceReloadLanguageSettings 函数，立即调用它
      if (typeof window.forceReloadLanguageSettings === 'function') {
        console.log('立即调用 forceReloadLanguageSettings 函数');
        window.forceReloadLanguageSettings();
      }

      // 重新加载格式选项
      addCopyFormatOptions();

      // 重新加载语言选项
      addLanguageOptions();

      // 恢复语言选择器的值
      languageSelect.value = selectedLanguage;

      // 显示语言已更改的消息
      showStatus(getI18nMessage('language_changed'), 'success');

      // 如果设置为自动，获取当前浏览器语言并显示
      if (selectedLanguage === 'auto') {
        const browserLang = chrome.i18n.getUILanguage();
        console.log('Auto language selected, browser language is:', browserLang);

        // 立即检查浏览器语言并通知所有页面
        chrome.runtime.sendMessage({
          action: 'checkBrowserLanguage'
        }).catch(error => {
          console.error('Failed to send checkBrowserLanguage message:', error);
        });
      } else {
        // 用户手动选择了语言，通知所有页面
        chrome.runtime.sendMessage({
          action: 'languageChanged',
          language: selectedLanguage
        }).catch(error => {
          console.error('Failed to send languageChanged message:', error);
        });
      }

      // 短暂延迟后重新加载页面，让用户看到状态消息
      setTimeout(() => {
        // 强制重新加载页面，应用新的语言设置
        window.location.reload();
      }, 1000);
    })
    .catch(error => {
      console.error('Failed to save language setting:', error);
      showStatus(getI18nMessage('settings_save_failed'), 'error');
    });
}

// 初始化设置页面
document.addEventListener('DOMContentLoaded', initialize);

// 将 addCopyFormatOptions 函数暴露为全局函数，以便 i18n.js 可以调用
window.addCopyFormatOptions = addCopyFormatOptions;
