/* 设置页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

header {
  margin-bottom: 30px;
  text-align: center;
}

h1 {
  font-size: 24px;
  color: #1a73e8;
}

h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #1a73e8;
}

.settings-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

select, input[type="number"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

input[type="number"] {
  max-width: 100px;
}

select:focus, input:focus {
  border-color: #1a73e8;
}

.description {
  font-size: 13px;
  color: #666;
  margin-top: 5px;
}

.toggle-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #1a73e8;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.shortcut-info,
.shortcut-tip {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.shortcut-tip {
  background-color: #e8f0fe;
  border-left: 4px solid #1a73e8;
  display: flex;
  align-items: flex-start;
  border-radius: 6px;
  margin-top: 15px;
  padding: 15px;
}

.tip-content {
  flex: 1;
}

.tip-content p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.shortcut-keys-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.shortcut-item {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.shortcut-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.shortcut-name {
  font-weight: 500;
  color: #202124;
  flex: 1;
}

.shortcut-keys {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.key-badge {
  display: inline-block;
  background-color: #f1f3f4;
  color: #202124;
  padding: 3px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #dadce0;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  min-width: 20px;
  text-align: center;
}

.key-plus {
  margin: 0 4px;
  color: #5f6368;
  font-weight: 500;
}

.os-label {
  color: #5f6368;
  font-size: 12px;
  margin-left: 5px;
}

code {
  background-color: #e8eaed;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
}

.buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn.primary {
  background-color: #1a73e8;
  color: white;
  text-decoration: none;
}

.btn.primary:hover {
  background-color: #1669d3;
}

.btn.secondary {
  background-color: #f1f3f4;
  color: #1a73e8;
  border: 1px solid #1a73e8;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn.secondary:hover {
  background-color: #e8eaed;
}

/* 剪贴板历史记录样式 */
.clipboard-history-section {
  position: relative;
  overflow: hidden;
}

.clipboard-history-icon {
  display: inline-block;
  margin-right: 10px;
  color: #1a73e8;
  font-size: 20px;
}

.clipboard-history-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px;
  margin-bottom: 10px;
  transition: all 0.2s;
  text-decoration: none;
  border-radius: 6px;
}

.clipboard-history-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #1669d3;
}

.windows, .mac {
  display: inline-block;
  margin-right: 10px;
}

.status {
  font-size: 14px;
  height: 20px;
  color: #1a73e8;
}
