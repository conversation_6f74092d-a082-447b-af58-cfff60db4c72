/**
 * 国际化辅助脚本
 * 用于在HTML页面中应用国际化消息
 */

// 存储上次检测到的浏览器语言
let lastDetectedBrowserLanguage = '';

// 检查浏览器语言是否变化
function checkBrowserLanguageChange() {
  const currentBrowserLanguage = chrome.i18n.getUILanguage();

  // 如果这是第一次检查，记录当前语言
  if (!lastDetectedBrowserLanguage) {
    lastDetectedBrowserLanguage = currentBrowserLanguage;
    console.log('初始浏览器语言:', lastDetectedBrowserLanguage);
    return false;
  }

  // 检查语言是否变化
  if (currentBrowserLanguage !== lastDetectedBrowserLanguage) {
    console.log('浏览器语言已变化:', lastDetectedBrowserLanguage, '->', currentBrowserLanguage);
    lastDetectedBrowserLanguage = currentBrowserLanguage;
    return true;
  }

  return false;
}

// 更新自动语言设置
function updateAutoLanguageSetting() {
  chrome.storage.sync.get(['language'], (result) => {
    const storedLanguage = result.language || 'auto';

    // 只有当设置为自动时才更新
    if (storedLanguage === 'auto') {
      const browserLang = chrome.i18n.getUILanguage();
      const displayLanguage = getBrowserLanguageCode(browserLang);
      console.log('自动语言设置更新:', displayLanguage);

      // 应用新的语言设置
      applyI18nMessages(displayLanguage);
    }
  });
}

// 定期检查浏览器语言变化
function startBrowserLanguageMonitor() {
  // 每分钟检查一次浏览器语言变化
  setInterval(() => {
    if (checkBrowserLanguageChange()) {
      updateAutoLanguageSetting();
    }
  }, 60000); // 60秒
}

// 强制重新加载语言设置
function forceReloadLanguageSettings() {
  // 获取浏览器UI语言
  const browserUILanguage = chrome.i18n.getUILanguage();
  // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
  const rawNavigatorLanguage = navigator.language || navigator.userLanguage;

  console.log('强制重新加载语言设置，Chrome API 浏览器语言:', browserUILanguage, '原始浏览器语言:', rawNavigatorLanguage);

  // 获取当前语言设置
  chrome.storage.sync.get(['language'], (result) => {
    let currentLanguage = result.language || 'auto';
    let displayLanguage = currentLanguage;

    // 如果是自动设置，则使用浏览器语言
    if (currentLanguage === 'auto') {
      // 根据浏览器语言确定使用哪种语言
      displayLanguage = getBrowserLanguageCode(browserUILanguage);
      console.log('强制重新加载，自动检测语言:', displayLanguage);

      // 如果检测到的语言不是英语，但原始浏览器语言不是中文，则强制使用英语
      if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
        console.log('检测到不一致，强制使用英语。检测语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
        displayLanguage = 'en';
      }
    } else {
      // 用户手动选择了语言，尊重用户的选择
      console.log('用户手动选择了语言:', currentLanguage);
      displayLanguage = currentLanguage;
    }

    // 应用国际化消息到页面元素
    console.log('最终使用的语言:', displayLanguage);
    currentDisplayLanguage = displayLanguage;
    applyI18nMessages(displayLanguage);

    // 通知其他页面更新语言设置
    try {
      chrome.runtime.sendMessage({
        action: 'languageDetected',
        detectedLanguage: displayLanguage
      }).catch(() => {
        // 忽略错误
      });
    } catch (e) {
      // 忽略错误
    }
  });
}

// 当DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  // 首先加载所有语言的消息
  ensureMessagesLoaded();

  // 获取浏览器UI语言
  const browserUILanguage = chrome.i18n.getUILanguage();
  // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
  const rawNavigatorLanguage = navigator.language || navigator.userLanguage;

  console.log('当前浏览器UI语言 (chrome.i18n.getUILanguage):', browserUILanguage);
  console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

  lastDetectedBrowserLanguage = browserUILanguage;

  // 直接从 Chrome 存储中获取语言设置
  // 不需要检查 localStorage

  // 获取当前语言设置
  chrome.storage.sync.get(['language'], (result) => {
    let currentLanguage = result.language || 'auto';
    let displayLanguage = currentLanguage;

    console.log('存储的语言设置:', currentLanguage);

    // 如果用户手动选择了语言，尊重用户的选择
    if (currentLanguage !== 'auto') {
      console.log('用户手动选择了语言:', currentLanguage);
      displayLanguage = currentLanguage;
    } else {
      // 如果是自动设置，则使用浏览器语言
      // 根据浏览器语言确定使用哪种语言
      displayLanguage = getBrowserLanguageCode(browserUILanguage);
      console.log('自动检测语言:', displayLanguage);

      // 如果检测到的语言不是英语，但原始浏览器语言不是中文，则强制使用英语
      if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
        console.log('检测到不一致，强制使用英语。检测语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
        displayLanguage = 'en';
      }
    }

    // 应用国际化消息到页面元素
    console.log('最终使用的语言:', displayLanguage);
    currentDisplayLanguage = displayLanguage;
    applyI18nMessages(displayLanguage);

    // 延迟一段时间后强制重新加载语言设置，确保语言设置能够正确应用
    setTimeout(forceReloadLanguageSettings, 1000);

    // 再次延迟，确保语言设置能够正确应用，但仅在自动模式下
    setTimeout(() => {
      chrome.storage.sync.get(['language'], (result) => {
        const storedLanguage = result.language || 'auto';
        // 仅在自动模式下进行强制检查
        if (storedLanguage === 'auto' && currentDisplayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
          console.log('再次检测到不一致，强制使用英语');
          currentDisplayLanguage = 'en';
          applyI18nMessages('en');
        }
      });
    }, 2000);
  });

  // 开始监控浏览器语言变化
  startBrowserLanguageMonitor();

// 监听语言变更消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'languageChanged') {
    console.log('收到语言变更消息:', message.language);
    // 用户手动选择了语言，直接应用
    currentDisplayLanguage = message.language;
    applyI18nMessages(message.language);
    sendResponse({ success: true });
    return true;
  }
});

  // 监听语言变化
  chrome.storage.onChanged.addListener((changes) => {
    if (changes.language) {
      console.log('Language setting changed:', changes.language);
      // 如果语言设置发生变化，重新应用国际化消息
      let newLanguage = changes.language.newValue;

      // 如果是自动设置，则使用浏览器语言
      let displayLanguage = newLanguage;
      if (newLanguage === 'auto') {
        const browserLang = chrome.i18n.getUILanguage();
        displayLanguage = getBrowserLanguageCode(browserLang);
        console.log('语言设置变更后自动检测语言:', displayLanguage);
      }

      applyI18nMessages(displayLanguage);
    }

    // 监听检测到的语言变化
    if (changes.detectedLanguage) {
      console.log('Detected language changed:', changes.detectedLanguage);

      // 获取当前语言设置
      chrome.storage.sync.get(['language'], (result) => {
        const storedLanguage = result.language || 'auto';

        // 如果设置为自动，则使用检测到的语言
        if (storedLanguage === 'auto') {
          const detectedLanguage = changes.detectedLanguage.newValue;
          console.log('自动语言模式，使用检测到的语言:', detectedLanguage);

          // 应用检测到的语言
          applyI18nMessages(detectedLanguage);
        }
      });
    }
  });

  // 监听来自背景脚本的消息
  chrome.runtime.onMessage.addListener((message) => {
    if (message.action === 'settingsUpdated') {
      // 处理语言设置更新
      if (message.settings && message.settings.language) {
        // 重新应用国际化消息
        let updatedLanguage = message.settings.language;

        // 如果是自动设置，则使用浏览器语言
        let displayLanguage = updatedLanguage;
        if (updatedLanguage === 'auto') {
          const browserLang = chrome.i18n.getUILanguage();
          displayLanguage = getBrowserLanguageCode(browserLang);
          console.log('消息更新后自动检测语言:', displayLanguage);
        }

        applyI18nMessages(displayLanguage);
      }

      // 处理检测到的语言更新
      if (message.settings && message.settings.detectedLanguage) {
        // 获取当前语言设置
        chrome.storage.sync.get(['language'], (result) => {
          const storedLanguage = result.language || 'auto';

          // 如果设置为自动，则使用检测到的语言
          if (storedLanguage === 'auto') {
            const detectedLanguage = message.settings.detectedLanguage;
            console.log('消息更新后使用检测到的语言:', detectedLanguage);

            // 应用检测到的语言
            applyI18nMessages(detectedLanguage);
          }
        });
      }
    } else if (message.action === 'checkBrowserLanguage') {
      // 手动触发浏览器语言检查
      if (checkBrowserLanguageChange()) {
        updateAutoLanguageSetting();
      }
    }
  });
});

/**
 * 应用国际化消息到页面元素
 * @param {string} language - 要应用的语言代码
 */
function applyI18nMessages(language) {
  // 设置当前显示语言
  currentDisplayLanguage = language;

  // 获取页面标题
  const pageTitle = document.title;

  // 根据页面类型应用不同的消息
  if (pageTitle === 'SmartCopy') {
    // 弹出窗口页面
    applyPopupMessages();
    // 更新页面标题
    document.title = getMessage('popup_title');
  } else if (pageTitle.includes('设置') || pageTitle.includes('Settings') || pageTitle.includes('SmartCopy')) {
    // 设置页面
    applyOptionsMessages();
  } else if (pageTitle.includes('剪贴板') || pageTitle.includes('Clipboard')) {
    // 剪贴板历史记录页面
    applyClipboardHistoryMessages();
  } else {
    // 未知页面类型，只应用通用消息
  }

  // 应用通用消息（所有页面都可能有的元素）
  applyCommonMessages();

  // 更新语言选择器（如果存在）
  updateLanguageSelector(language);

  // 如果页面中有格式选择器，重新加载格式选项
  const formatSelect = document.getElementById('format-select');
  if (formatSelect && window.initialize) {
    window.initialize();
  }

  // 如果页面中有复制格式选择器，重新加载格式选项
  const copyFormatSelect = document.getElementById('copy-format');
  if (copyFormatSelect && window.addCopyFormatOptions) {
    window.addCopyFormatOptions();
  }


}

/**
 * 更新语言选择器的选中项
 * @param {string} displayLanguage - 当前显示语言
 */
function updateLanguageSelector(displayLanguage) {
  const languageSelect = document.getElementById('language-select');
  if (languageSelect) {
    // 从存储中获取实际的语言设置
    chrome.storage.sync.get(['language'], (result) => {
      const storedLanguage = result.language || 'auto';
      languageSelect.value = storedLanguage;
      console.debug(`[i18n] 更新语言选择器: 存储语言=${storedLanguage}, 显示语言=${displayLanguage}`);
    });
  }
}

/**
 * 应用弹出窗口页面的国际化消息
 */
function applyPopupMessages() {
  // 设置页面标题
  setTextContent('popup-title', 'popup_title');

  // 设置格式选择器标签
  setTextContent('default-copy-format', 'default_copy_format');

  // 设置按钮文本
  setTextContent('copy-current-tab', 'copy_current_tab');
  setTextContent('copy-all-tabs', 'copy_all_tabs');
  setTextContent('clipboard-history', 'clipboard_history');

  // 设置开关标签
  setTextContent('auto-copy-text', 'auto_copy_text');
  setTextContent('super-copy', 'super_copy');

  // 设置高级设置按钮
  setTextContent('open-options', 'advanced_settings');
}

/**
 * 应用设置页面的国际化消息
 */
function applyOptionsMessages() {
  // 保存当前选中的值
  const copyFormatSelect = document.getElementById('copy-format');
  const copyTextFormatSelect = document.getElementById('copy-text-format');
  const languageSelect = document.getElementById('language-select');

  const currentCopyFormat = copyFormatSelect ? copyFormatSelect.value : null;
  const currentTextFormat = copyTextFormatSelect ? copyTextFormatSelect.value : null;
  const currentLanguage = languageSelect ? languageSelect.value : null;

  console.log('应用设置页面国际化消息前保存当前值:', {
    copyFormat: currentCopyFormat,
    textFormat: currentTextFormat,
    language: currentLanguage
  });

  // 设置页面标题
  document.title = getMessage('options_title');
  setTextContent('options-title', 'options_title');

  // 设置部分标题
  setTextContent('copy-format-section-title', 'copy_format_settings');
  setTextContent('auto-copy-section-title', 'auto_copy_settings');
  setTextContent('clipboard-history-section-title', 'clipboard_history_section');
  setTextContent('language-settings-title', 'language_settings');

  // 设置表单标签
  setTextContent('copy-format-label', 'default_copy_format');
  setTextContent('auto-copy-label', 'enable_auto_copy');
  setTextContent('min-characters-label', 'min_characters');
  setTextContent('copy-text-format-label', 'copy_text_format');
  setTextContent('max-history-items-label', 'max_history_items');
  setTextContent('language-label', 'language');

  // 设置描述文本
  setTextContent('min-characters-desc', 'min_characters_desc');
  setTextContent('copy-text-format-desc', 'copy_text_format_desc');
  setTextContent('clipboard-history-desc', 'clipboard_history_desc');
  setTextContent('max-history-items-desc', 'max_history_items_desc');
  setTextContent('language-desc', 'language_desc');

  // 设置按钮文本
  setTextContent('open-clipboard-history', 'view_clipboard_history');
  setTextContent('view-clipboard-history-desc', 'view_clipboard_history_desc');
  setTextContent('save-btn', 'save_settings');
  setTextContent('reset-btn', 'reset_defaults');

  // 设置选项文本
  setSelectOptions('copy-text-format', [
    { value: 'plain', messageKey: 'plain_text' },
    { value: 'markdown', messageKey: 'markdown' }
  ]);

  // 设置语言选项
  setSelectOptions('language-select', [
    { value: 'auto', messageKey: 'language_auto' },
    { value: 'en', messageKey: 'language_en' },
    { value: 'zh_CN', messageKey: 'language_zh_CN' },
    { value: 'zh_TW', messageKey: 'language_zh_TW' }
  ]);

  // 恢复之前选中的值
  setTimeout(() => {
    // 从存储中获取最新的设置值
    chrome.storage.sync.get(['copyFormat', 'copyTextFormat', 'language'], function(result) {
      console.log('从存储中获取到的设置值:', result);

      // 恢复标签页复制格式
      if (copyFormatSelect) {
        const formatToRestore = currentCopyFormat || result.copyFormat;
        if (formatToRestore) {
          console.log('恢复标签页复制格式为:', formatToRestore);
          copyFormatSelect.value = formatToRestore;
        }
      }

      // 恢复选中文字复制格式
      if (copyTextFormatSelect) {
        const textFormatToRestore = currentTextFormat || result.copyTextFormat;
        if (textFormatToRestore) {
          console.log('恢复选中文字复制格式为:', textFormatToRestore);
          copyTextFormatSelect.value = textFormatToRestore;
        }
      }

      // 恢复语言设置
      if (languageSelect) {
        const languageToRestore = currentLanguage || result.language;
        if (languageToRestore) {
          console.log('恢复语言设置为:', languageToRestore);
          languageSelect.value = languageToRestore;
        }
      }
    });
  }, 100);
}

/**
 * 应用剪贴板历史记录页面的国际化消息
 */
function applyClipboardHistoryMessages() {
  // 设置页面标题
  document.title = getMessage('clipboard_history_title');
  setTextContent('clipboard-history-title', 'clipboard_history_title');

  // 设置按钮文本
  setTextContent('refresh-btn', 'refresh');
  setTextContent('clear-history-btn', 'clear_history');

  // 设置标签文本
  setTextContent('items-per-page-label', 'items_per_page');

  // 设置空状态文本
  setTextContent('empty-state-title', 'empty_history');
  setTextContent('empty-state-desc', 'empty_history_desc');

  // 设置加载状态文本
  setTextContent('loading-text', 'loading');

  // 设置分页按钮
  setTextContent('prev-page', 'prev_page');
  setTextContent('next-page', 'next_page');

  // 设置通知关闭按钮
  if (document.getElementById('notification-close')) {
    document.getElementById('notification-close').innerHTML = '&times;';
  }

  // 确保所有动态创建的元素也使用正确的国际化消息
  // 这些元素可能在页面加载后由 clipboard-history-ui.js 创建
  // 我们需要确保 window.getMessage 函数可用
  window.updateDynamicElements = function() {
    // 展开按钮
    const expandButtons = document.querySelectorAll('.expand-btn');
    expandButtons.forEach(button => {
      if (button.textContent.includes('展开') || button.textContent.includes('Expand')) {
        button.textContent = getMessage('expand');
      } else if (button.textContent.includes('收起') || button.textContent.includes('Collapse')) {
        button.textContent = getMessage('collapse');
      }
    });

    // 复制按钮
    const copyButtons = document.querySelectorAll('.copy-btn span');
    copyButtons.forEach(span => {
      span.textContent = getMessage('copy');
    });

    // 删除按钮
    const deleteButtons = document.querySelectorAll('.delete-btn span');
    deleteButtons.forEach(span => {
      span.textContent = getMessage('delete');
    });

    // 源标签
    const sourceTags = document.querySelectorAll('.source-tag');
    sourceTags.forEach(tag => {
      const text = tag.textContent;
      if (text.includes('自动复制') || text.includes('自動複製') ||
          text.includes('选中文字') || text.includes('選中文字') ||
          text.includes('Auto-copied')) {
        if (text.includes('(') && text.includes(')')) {
          // 检查是否包含未替换的占位符 ($1)
          if (text.includes('($1)')) {
            console.log('检测到未替换的占位符 ($1)，尝试修复');
            // 默认使用纯文本格式
            const formatKey = 'plain_text';
            const formatText = getMessage(formatKey);
            let sourceText = getMessage('auto_copy_source', [formatText]);

            // 检查结果是否包含未替换的占位符
            if (sourceText && sourceText.includes('($1)')) {
              console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, formatText);
              // 手动替换占位符
              // 检查替换值是否已经包含括号
              if (formatText.startsWith('(') && formatText.endsWith(')')) {
                // 如果替换值已经有括号，直接使用替换值
                sourceText = sourceText.replace('($1)', formatText);
              } else {
                // 否则添加括号
                sourceText = sourceText.replace('($1)', `(${formatText})`);
              }
            }

            tag.textContent = sourceText;
            console.log('修复后的源标签:', tag.textContent);
          } else {
            const formatMatch = text.match(/\(([^)]+)\)/);
            if (formatMatch && formatMatch[1]) {
              const formatName = formatMatch[1];
              let formatKey = '';
              if (formatName.includes('纯文本') || formatName.includes('純文本') ||
                  formatName.toLowerCase().includes('plain text')) {
                formatKey = 'plain_text';
              } else if (formatName.toLowerCase().includes('markdown')) {
                formatKey = 'markdown';
              }

              if (formatKey) {
                // 确保正确传递参数
                const formatText = getMessage(formatKey);
                let sourceText = getMessage('auto_copy_source', [formatText]);

                // 检查结果是否包含未替换的占位符
                if (sourceText && sourceText.includes('($1)')) {
                  console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, formatText);
                  // 手动替换占位符
                  // 检查替换值是否已经包含括号
                  if (formatText.startsWith('(') && formatText.endsWith(')')) {
                    // 如果替换值已经有括号，直接使用替换值
                    sourceText = sourceText.replace('($1)', formatText);
                  } else {
                    // 否则添加括号
                    sourceText = sourceText.replace('($1)', `(${formatText})`);
                  }
                }

                tag.textContent = sourceText;
                console.log('Setting source tag with format:', formatKey, formatText, sourceText);
              } else {
                tag.textContent = getMessage('auto_copy_text');
              }
            } else {
              tag.textContent = getMessage('auto_copy_text');
            }
          }
        } else {
          tag.textContent = getMessage('auto_copy_text');
        }
      } else if (text.includes('手动复制') || text.includes('手動複製') ||
                text.includes('Manual copy')) {
        tag.textContent = getMessage('manual_copy');
      } else if (text.includes('当前标签') || text.includes('Current tab')) {
        tag.textContent = getMessage('copy_current_tab');
      } else if (text.includes('所有标签') || text.includes('All tabs')) {
        tag.textContent = getMessage('copy_all_tabs');
      }
    });
  };

  // 在页面加载完成后调用一次
  setTimeout(function() {
    if (window.updateDynamicElements) {
      window.updateDynamicElements();
    }
  }, 500);
}

/**
 * 应用通用消息（所有页面都可能有的元素）
 */
function applyCommonMessages() {
  // 查找所有带有data-i18n属性的元素
  const i18nElements = document.querySelectorAll('[data-i18n]');
  i18nElements.forEach(element => {
    const messageKey = element.getAttribute('data-i18n');
    element.textContent = getMessage(messageKey);
  });
}

/**
 * 设置元素的文本内容为国际化消息
 * @param {string} elementId - 元素ID
 * @param {string} messageKey - 消息键
 */
function setTextContent(elementId, messageKey) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = getMessage(messageKey);
  }
}

/**
 * 设置选择器的选项为国际化消息
 * @param {string} selectId - 选择器ID
 * @param {Array} options - 选项数组，每个选项包含value和messageKey
 */
function setSelectOptions(selectId, options) {
  const select = document.getElementById(selectId);
  if (select) {
    // 保存当前选中的值
    const currentValue = select.value;
    console.log(`保存选择器 ${selectId} 当前选中的值:`, currentValue);

    // 如果当前没有选中值，尝试从存储中获取
    let valueFromStorage = null;
    if (!currentValue || currentValue === '') {
      if (selectId === 'copy-format') {
        chrome.storage.sync.get(['copyFormat'], function(result) {
          if (result.copyFormat) {
            valueFromStorage = result.copyFormat;
            console.log(`从存储中获取到 copyFormat:`, valueFromStorage);
            // 设置选择器的值
            select.value = valueFromStorage;
          }
        });
      } else if (selectId === 'copy-text-format') {
        chrome.storage.sync.get(['copyTextFormat'], function(result) {
          if (result.copyTextFormat) {
            valueFromStorage = result.copyTextFormat;
            console.log(`从存储中获取到 copyTextFormat:`, valueFromStorage);
            // 设置选择器的值
            select.value = valueFromStorage;
          }
        });
      }
    }

    options.forEach(option => {
      // 检查选项是否已存在
      let optionElement = Array.from(select.options).find(opt => opt.value === option.value);

      if (!optionElement) {
        // 如果选项不存在，创建新选项
        optionElement = document.createElement('option');
        optionElement.value = option.value;
        select.appendChild(optionElement);
      }

      // 设置选项文本
      optionElement.textContent = getMessage(option.messageKey);
    });

    // 恢复之前选中的值
    if (currentValue) {
      console.log(`恢复选择器 ${selectId} 的值为:`, currentValue);
      select.value = currentValue;

      // 如果恢复失败，记录警告
      if (select.value !== currentValue) {
        console.warn(`恢复选择器 ${selectId} 的值失败，当前值:`, select.value, '期望值:', currentValue);
      }
    }
  }
}

// 当前使用的语言
let currentDisplayLanguage = 'auto';

// 预加载的消息缓存
const messagesCache = {
  en: {},
  zh_CN: {},
  zh_TW: {}
};

// 加载消息文件
async function loadMessages() {
  try {
    // 加载英文消息
    const enResponse = await fetch(chrome.runtime.getURL('_locales/en/messages.json'));
    messagesCache.en = await enResponse.json();

    // 加载简体中文消息
    const zhCNResponse = await fetch(chrome.runtime.getURL('_locales/zh_CN/messages.json'));
    messagesCache.zh_CN = await zhCNResponse.json();

    // 加载繁体中文消息
    const zhTWResponse = await fetch(chrome.runtime.getURL('_locales/zh_TW/messages.json'));
    messagesCache.zh_TW = await zhTWResponse.json();

    console.log('所有语言消息已加载完成');

    // 加载完成后，重新应用国际化消息
    chrome.storage.sync.get(['language'], (result) => {
      const storedLanguage = result.language || 'auto';
      let displayLanguage = storedLanguage;

      console.log('存储的语言设置:', storedLanguage);

      // 如果用户手动选择了语言，尊重用户的选择
      if (storedLanguage !== 'auto') {
        console.log('用户手动选择了语言:', storedLanguage);
        displayLanguage = storedLanguage;
      } else {
        // 如果是自动设置，则使用浏览器语言
        const browserLang = chrome.i18n.getUILanguage();
        // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
        const rawNavigatorLanguage = navigator.language || navigator.userLanguage;

        displayLanguage = getBrowserLanguageCode(browserLang);
        console.log('加载消息时自动检测语言:', displayLanguage, 'Chrome API 浏览器语言:', browserLang, '原始浏览器语言:', rawNavigatorLanguage);

        // 如果检测到的语言不是英语，但原始浏览器语言不是中文，则强制使用英语
        if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
          console.log('检测到不一致，强制使用英语。检测语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
          displayLanguage = 'en';
        }
      }

      // 强制记录当前使用的语言
      console.log('当前使用的语言设置:', storedLanguage, '最终显示语言:', displayLanguage);

      currentDisplayLanguage = displayLanguage;
      applyI18nMessages(displayLanguage);

      // 标记消息已加载
      messagesLoaded = true;

      // 延迟一段时间后再次检查，确保语言设置能够正确应用，但仅在自动模式下
      setTimeout(() => {
        chrome.storage.sync.get(['language'], (result) => {
          const currentStoredLanguage = result.language || 'auto';
          // 仅在自动模式下进行强制检查
          if (currentStoredLanguage === 'auto') {
            const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
            if (currentDisplayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
              console.log('再次检测到不一致，强制使用英语');
              currentDisplayLanguage = 'en';
              applyI18nMessages('en');
            }
          }
        });
      }, 1000);
    });
  } catch (error) {
    console.error('Error loading messages:', error);
  }
}

// 标记是否已加载消息
let messagesLoaded = false;

// 在页面加载时预加载消息，但确保只加载一次
async function ensureMessagesLoaded() {
  if (!messagesLoaded) {
    await loadMessages();
    messagesLoaded = true;
  }
}

// 立即加载消息文件
loadMessages();

/**
 * 获取国际化消息
 * @param {string} messageKey - 消息键
 * @param {Array|string} substitutions - 替换参数，可以是字符串或字符串数组
 * @returns {string} 国际化消息
 */
function getMessage(messageKey, substitutions) {
  // 尝试确保消息已加载
  if (!messagesLoaded) {
    // 不显示警告，直接使用Chrome的i18n API
    return chrome.i18n.getMessage(messageKey, substitutions) || messageKey;
  }

  // 记录当前使用的语言
  console.debug(`[i18n] getMessage 被调用，消息键: ${messageKey}, 当前语言: ${currentDisplayLanguage}`);

  let message = '';
  // 根据当前显示语言获取消息
  if (currentDisplayLanguage === 'en') {
    // 尝试从缓存中获取英文消息
    if (messagesCache.en[messageKey] && messagesCache.en[messageKey].message) {
      message = messagesCache.en[messageKey].message;
      console.debug(`[i18n] 从英文缓存获取消息: ${messageKey}`);
    }
  } else if (currentDisplayLanguage === 'zh_CN') {
    // 尝试从缓存中获取简体中文消息
    if (messagesCache.zh_CN[messageKey] && messagesCache.zh_CN[messageKey].message) {
      message = messagesCache.zh_CN[messageKey].message;
      console.debug(`[i18n] 从简体中文缓存获取消息: ${messageKey}`);
    }
  } else if (currentDisplayLanguage === 'zh_TW') {
    // 尝试从缓存中获取繁体中文消息
    if (messagesCache.zh_TW[messageKey] && messagesCache.zh_TW[messageKey].message) {
      message = messagesCache.zh_TW[messageKey].message;
      console.debug(`[i18n] 从繁体中文缓存获取消息: ${messageKey}`);
    }
  } else {
    // 如果当前语言不是支持的语言，强制使用英语
    console.debug(`[i18n] 当前语言 ${currentDisplayLanguage} 不受支持，使用英语`);
    if (messagesCache.en[messageKey] && messagesCache.en[messageKey].message) {
      message = messagesCache.en[messageKey].message;
      console.debug(`[i18n] 从英文缓存获取消息: ${messageKey}`);
    }
  }

  // 如果从缓存中没有找到消息，尝试使用Chrome的i18n API
  if (!message) {
    message = chrome.i18n.getMessage(messageKey, substitutions);
    console.debug(`[i18n] 从Chrome API获取消息: ${messageKey}, 结果: ${message}`);
  } else if (substitutions) {
    // 如果有替换参数，手动进行替换
    if (Array.isArray(substitutions)) {
      // 如果是数组，按顺序替换 $1, $2, ... 以及 ($1), ($2), ...
      substitutions.forEach((substitution, index) => {
        const placeholder = `$${index + 1}`;
        message = message.replace(new RegExp(placeholder, 'g'), substitution);

        // 同时替换括号形式的占位符 ($1), ($2), ...
        const bracketPlaceholder = `\\(\\$${index + 1}\\)`;
        // 检查替换值是否已经包含括号
        if (substitution.startsWith('(') && substitution.endsWith(')')) {
          // 如果替换值已经有括号，直接使用替换值
          message = message.replace(new RegExp(bracketPlaceholder, 'g'), substitution);
        } else {
          // 否则添加括号
          message = message.replace(new RegExp(bracketPlaceholder, 'g'), `(${substitution})`);
        }
      });
    } else if (typeof substitutions === 'string') {
      // 如果是字符串，替换 $1 和 ($1)
      message = message.replace(/\$1/g, substitutions);

      // 检查替换值是否已经包含括号
      if (substitutions.startsWith('(') && substitutions.endsWith(')')) {
        // 如果替换值已经有括号，直接使用替换值
        message = message.replace(/\(\$1\)/g, substitutions);
      } else {
        // 否则添加括号
        message = message.replace(/\(\$1\)/g, `(${substitutions})`);
      }
    } else if (typeof substitutions === 'object' && substitutions !== null) {
      // 如果是对象，根据占位符名称进行替换
      console.debug(`[i18n] 使用对象替换参数: ${JSON.stringify(substitutions)}`);

      // 替换 $COUNT$ 和 $Count$ 占位符
      if (substitutions.count || substitutions.COUNT) {
        const countValue = substitutions.count || substitutions.COUNT;
        message = message.replace(/\$COUNT\$/g, countValue);
        message = message.replace(/\$Count\$/g, countValue);
        // 检查是否有未替换的占位符
        if (message.includes('$COUNT$') || message.includes('$Count$')) {
          console.warn('仍然存在未替换的占位符，尝试直接替换:', message);
          // 直接替换占位符
          message = message.replace(/\$COUNT\$/g, countValue);
          message = message.replace(/\$Count\$/g, countValue);
        }
      }

      // 替换 $FORMAT$ 占位符
      if (substitutions.format || substitutions.FORMAT) {
        const formatValue = substitutions.format || substitutions.FORMAT;
        message = message.replace(/\$FORMAT\$/g, formatValue);
      }

      // 替换 $ERROR$ 占位符
      if (substitutions.error || substitutions.ERROR) {
        const errorValue = substitutions.error || substitutions.ERROR;
        message = message.replace(/\$ERROR\$/g, errorValue);
      }

      // 替换 $SITE$ 占位符
      if (substitutions.site || substitutions.SITE) {
        const siteValue = substitutions.site || substitutions.SITE;
        message = message.replace(/\$SITE\$/g, siteValue);
      }

      // 替换 $CURRENT$ 占位符
      if (substitutions.current || substitutions.CURRENT) {
        const currentValue = substitutions.current || substitutions.CURRENT;
        message = message.replace(/\$CURRENT\$/g, currentValue);
      }

      // 替换 $TOTAL$ 占位符
      if (substitutions.total || substitutions.TOTAL) {
        const totalValue = substitutions.total || substitutions.TOTAL;
        message = message.replace(/\$TOTAL\$/g, totalValue);
      }
    }

    // 检查是否仍然包含未替换的占位符
    if (message.includes('$1') || message.includes('$2') || message.includes('$3') || message.includes('($1)') || message.includes('$COUNT$')) {
      console.warn(`Message still contains placeholders after substitution: ${message}`);

      // 尝试使用默认值替换
      if ((message.includes('$1') || message.includes('($1)')) && messageKey === 'auto_copy_source') {
        // 对于 auto_copy_source，使用 plain_text 作为默认值
        const defaultFormat = getMessage('plain_text');

        // 替换 $1 和 ($1) 两种形式的占位符
        message = message.replace(/\$1/g, defaultFormat);

        // 检查替换值是否已经包含括号
        if (defaultFormat.startsWith('(') && defaultFormat.endsWith(')')) {
          // 如果替换值已经有括号，直接使用替换值
          message = message.replace(/\(\$1\)/g, defaultFormat);
        } else {
          // 否则添加括号
          message = message.replace(/\(\$1\)/g, `(${defaultFormat})`);
        }

        console.log(`Applied default format '${defaultFormat}' to message: ${message}`);
      }

      // 特殊处理 added_to_multi_selection 消息
      if (messageKey === 'added_to_multi_selection' && message.includes('$COUNT$')) {
        console.log('特殊处理 added_to_multi_selection 消息:', message);

        // 如果有替换参数，使用第一个参数替换 $COUNT$
        if (Array.isArray(substitutions) && substitutions.length > 0) {
          const countValue = substitutions[0];
          message = message.replace(/\$COUNT\$/g, countValue);
          console.log('替换后的消息:', message);
        }
      }
    }
  }

  // 调试日志 - 只在开发模式下显示
  if (!message) {
    // 只有在找不到消息时才显示警告
    console.warn(`No message found for key: ${messageKey} (${currentDisplayLanguage})`);
  }

  // 如果消息为空或未定义，返回消息键
  return message || messageKey;
}

// 根据浏览器语言获取对应的语言代码
function getBrowserLanguageCode(browserLang) {
  console.log('getBrowserLanguageCode 被调用，浏览器语言:', browserLang);

  // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
  const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
  console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

  // 检查浏览器语言并返回对应的支持语言代码
  if (browserLang.startsWith('zh') || rawNavigatorLanguage.startsWith('zh')) {
    if (browserLang.includes('TW') || browserLang.includes('HK') || browserLang.includes('MO') ||
        rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
      console.log('检测到繁体中文 (zh_TW)');
      return 'zh_TW'; // 繁体中文
    } else {
      console.log('检测到简体中文 (zh_CN)');
      return 'zh_CN'; // 简体中文
    }
  } else {
    // 对于所有非中文语言，返回英语
    console.log('检测到非中文语言，使用英语 (en)，浏览器语言:', browserLang, '原始语言:', rawNavigatorLanguage);
    return 'en'; // 默认英语
  }
}

// 强制使用英语，但仅在自动模式下
function forceUseEnglish() {
  // 首先检查当前语言设置
  chrome.storage.sync.get(['language'], (result) => {
    const storedLanguage = result.language || 'auto';

    // 仅在自动模式下强制使用英语
    if (storedLanguage === 'auto') {
      console.log('自动模式下强制使用英语');
      currentDisplayLanguage = 'en';
      applyI18nMessages('en');

      // 通知其他页面更新语言设置
      try {
        chrome.runtime.sendMessage({
          action: 'languageDetected',
          detectedLanguage: 'en'
        }).catch(() => {
          // 忽略错误
        });
      } catch (e) {
        // 忽略错误
      }
    } else {
      console.log('用户手动选择了语言:', storedLanguage, '，不强制使用英语');
    }
  });
}

// 将函数暴露为全局函数，以便其他脚本可以使用
window.getMessage = getMessage;
window.forceUseEnglish = forceUseEnglish;
window.forceReloadLanguageSettings = forceReloadLanguageSettings;

// 在页面加载完成后延迟执行，确保语言设置能够正确应用
setTimeout(() => {
  chrome.storage.sync.get(['language'], (result) => {
    const storedLanguage = result.language || 'auto';
    const rawNavigatorLanguage = navigator.language || navigator.userLanguage;

    // 仅在自动模式下且非中文环境下强制使用英语
    if (storedLanguage === 'auto' && !rawNavigatorLanguage.startsWith('zh')) {
      console.log('页面加载完成后检测到自动模式下的非中文环境，强制使用英语');
      forceUseEnglish();
    } else if (storedLanguage !== 'auto') {
      console.log('用户手动选择了语言:', storedLanguage, '，不强制使用英语');
    }
  });
}, 3000);
