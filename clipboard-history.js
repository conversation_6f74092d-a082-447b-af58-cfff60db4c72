// 剪贴板历史记录管理
const DEFAULT_MAX_HISTORY_ITEMS = 20; // 默认最大历史记录数量

// 剪贴板历史记录管理类
class ClipboardHistory {
  constructor() {
    this.historyItems = [];
    this.maxHistoryItems = DEFAULT_MAX_HISTORY_ITEMS;

    // 异步初始化
    this.initialized = false;
    this.initialize();
  }

  // 检查文本是否包含Markdown特征
  hasMarkdownFeatures(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }

    // 检查是否包含Markdown语法特征
    return (
      /(\*\*|__).+?(\*\*|__)/g.test(text) || // 粗体
      /(\*|_).+?(\*|_)/g.test(text) || // 斜体
      /\[.+?\]\(.+?\)/g.test(text) || // 链接
      /!\[.+?\]\(.+?\)/g.test(text) || // 图片
      /^#{1,6}\s.+$/gm.test(text) || // 标题
      /^>\s.+$/gm.test(text) || // 引用
      /^-\s.+$/gm.test(text) || // 无序列表
      /^[0-9]+\.\s.+$/gm.test(text) || // 有序列表
      /```[\s\S]*?```/g.test(text) || // 代码块
      /^```\w*$/m.test(text) // 代码块开始标记
    );
  }

  // 初始化
  async initialize() {
    try {
      console.log('初始化剪贴板历史记录管理');
      await this.loadHistory();
      await this.loadSettings();
      this.initialized = true;
      console.log('剪贴板历史记录管理初始化完成，共', this.historyItems.length, '条记录');
    } catch (error) {
      console.error('初始化剪贴板历史记录管理失败:', error);
      this.initialized = true; // 即使失败也标记为已初始化，避免无限重试
    }
  }

  // 加载历史记录
  async loadHistory() {
    try {
      console.log('加载剪贴板历史记录');
      const result = await chrome.storage.local.get('clipboardHistory');

      if (result && result.clipboardHistory && Array.isArray(result.clipboardHistory)) {
        this.historyItems = result.clipboardHistory;

        // 处理所有历史记录中的源标签，确保使用正确的国际化消息
        this.historyItems = this.historyItems.map(item => {
          if (item.source) {
            // 检查是否包含中文字符
            if (/[\u4e00-\u9fa5]/.test(item.source)) {
              // 如果是中文源标签，根据内容判断类型
              if (item.source === '自动复制选中文本' ||
                  item.source === '自動複製選中文本' ||
                  item.source === '选中文字自动复制' ||
                  item.source === '選中文字自動複製' ||
                  item.source === '自动复制选中文本 ()' ||
                  item.source === '自動複製選中文本 ()') {
                item.source = chrome.i18n.getMessage('auto_copy_text');
              } else if (item.source.includes('(') && item.source.includes(')')) {
                // 尝试提取格式信息
                const formatMatch = item.source.match(/\(([^)]+)\)/);
                if (formatMatch && formatMatch[1]) {
                  const formatName = formatMatch[1];
                  // 确定格式类型
                  let formatKey = '';
                  if (formatName.includes('纯文本') || formatName.includes('純文本')) {
                    formatKey = 'plain_text';
                  } else if (formatName.toLowerCase().includes('markdown')) {
                    formatKey = 'markdown';
                  }

                  if (formatKey) {
                    // 使用国际化消息重新构建源文本
                    const formatText = chrome.i18n.getMessage(formatKey);
                    item.source = chrome.i18n.getMessage('auto_copy_source', [formatText]);
                    console.log('Setting item.source with format in loadHistory:', formatKey, formatText);
                  }
                }
              }
            }
          }
          return item;
        });

        console.log('成功加载剪贴板历史记录:', this.historyItems.length, '条记录');
      } else {
        console.log('存储中没有历史记录或格式不正确，使用空数组');
        this.historyItems = [];
      }

      return this.historyItems;
    } catch (error) {
      console.error('加载剪贴板历史记录失败:', error);
      this.historyItems = [];
      return [];
    }
  }

  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get('maxHistoryItems');
      if (result && result.maxHistoryItems && typeof result.maxHistoryItems === 'number') {
        this.maxHistoryItems = result.maxHistoryItems;
      } else {
        this.maxHistoryItems = DEFAULT_MAX_HISTORY_ITEMS;
      }
      console.log('加载最大历史记录数量设置:', this.maxHistoryItems);
      return this.maxHistoryItems;
    } catch (error) {
      console.error('加载设置失败:', error);
      this.maxHistoryItems = DEFAULT_MAX_HISTORY_ITEMS;
      return DEFAULT_MAX_HISTORY_ITEMS;
    }
  }

  // 保存历史记录
  async saveHistory() {
    try {
      if (!Array.isArray(this.historyItems)) {
        console.error('历史记录不是数组，无法保存');
        return false;
      }

      console.log('保存剪贴板历史记录，共', this.historyItems.length, '条');
      await chrome.storage.local.set({ clipboardHistory: this.historyItems });
      console.log('剪贴板历史记录保存成功');
      return true;
    } catch (error) {
      console.error('保存剪贴板历史记录失败:', error);
      return false;
    }
  }

  // 添加新的历史记录
  async addItem(text, source = chrome.i18n.getMessage('manual_copy')) {
    // 检查 source 是否包含 auto_copy_source 消息模板中的占位符 $1
    if (source && source.includes('($1)')) {
      console.log('检测到 source 包含未替换的占位符: ', source);
      // 尝试提取格式信息
      const formatMatch = source.match(/auto-copied text \(([^)]+)\)/i);
      if (formatMatch && formatMatch[1] === '$1') {
        // 默认使用纯文本格式
        const formatKey = 'plain_text';
        const formatText = chrome.i18n.getMessage(formatKey);
        let sourceText = chrome.i18n.getMessage('auto_copy_source', [formatText]);

        // 检查结果是否包含未替换的占位符
        if (sourceText && sourceText.includes('($1)')) {
          console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, formatText);
          // 手动替换占位符
          sourceText = sourceText.replace('($1)', `(${formatText})`);
        }

        source = sourceText;
        console.log('修复 source 中的占位符，新值: ', source);
      }
    }
    // 确保初始化完成
    if (!this.initialized) {
      console.log('等待初始化完成...');
      await new Promise(resolve => {
        const checkInitialized = () => {
          if (this.initialized) {
            resolve();
          } else {
            setTimeout(checkInitialized, 100);
          }
        };
        checkInitialized();
      });
    }

    try {
      // 如果文本为空，不添加到历史记录
      if (!text || typeof text !== 'string' || text.trim() === '') {
        console.log('文本为空，不添加到历史记录');
        return null;
      }

      console.log('添加新历史记录:', text.substring(0, 30) + (text.length > 30 ? '...' : ''), '来源:', source);

      // 强制重新加载最新数据
      await this.loadHistory();

      // 如果已存在相同内容，先移除旧的
      const existingIndex = this.historyItems.findIndex(item => item.text === text);
      if (existingIndex !== -1) {
        console.log('已存在相同内容，移除旧记录');
        this.historyItems.splice(existingIndex, 1);
      }

      // 检查文本内容是否包含Markdown特征
      let finalSource = source;
      if (text && this.hasMarkdownFeatures(text)) {
        console.log('检测到新添加的文本包含Markdown特征，设置格式为markdown');
        const formatKey = 'markdown';
        const formatText = chrome.i18n.getMessage(formatKey);
        let sourceText = chrome.i18n.getMessage('auto_copy_source', [formatText]);

        // 检查结果是否包含未替换的占位符
        if (sourceText && sourceText.includes('($1)')) {
          console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, formatText);
          // 手动替换占位符
          sourceText = sourceText.replace('($1)', `(${formatText})`);
        }

        finalSource = sourceText;
        console.log('已将新添加项的格式设置为Markdown:', finalSource);
      }

      // 添加新记录到开头
      const newItem = {
        id: Date.now(),
        text,
        source: finalSource,
        timestamp: new Date().toISOString()
      };

      this.historyItems.unshift(newItem);

      // 限制历史记录数量
      if (this.historyItems.length > this.maxHistoryItems) {
        console.log('历史记录超出限制，裁剪至', this.maxHistoryItems, '条');
        this.historyItems = this.historyItems.slice(0, this.maxHistoryItems);
      }

      // 直接保存到存储
      try {
        await chrome.storage.local.set({ clipboardHistory: this.historyItems });
        console.log('新历史记录已添加并直接保存到存储，ID:', newItem.id);

        // 通知其他实例更新
        try {
          await chrome.runtime.sendMessage({
            action: 'clipboardHistoryUpdated',
            operation: 'add',
            item: newItem
          });
        } catch (msgError) {
          console.log('通知其他实例失败，可能是接收方未准备好:', msgError);
        }

        return newItem;
      } catch (saveError) {
        console.error('保存新记录失败:', saveError);
        return newItem; // 仍然返回新记录，即使保存失败
      }
    } catch (error) {
      console.error('添加历史记录失败:', error);
      return null;
    }
  }

  // 获取所有历史记录
  async getAll() {
    // 确保初始化完成
    if (!this.initialized) {
      console.log('获取历史记录前等待初始化完成...');
      await new Promise(resolve => {
        const checkInitialized = () => {
          if (this.initialized) {
            resolve();
          } else {
            setTimeout(checkInitialized, 100);
          }
        };
        checkInitialized();
      });
    }

    // 重新加载以确保数据最新
    try {
      await this.loadHistory();
    } catch (error) {
      console.error('重新加载历史记录失败:', error);
      // 继续使用当前缓存
    }

    // 在返回之前，确保所有源标签都使用正确的国际化消息
    const processedItems = this.historyItems.map(item => {
      // 创建一个新对象，避免修改原始对象
      const newItem = {...item};

      // 首先检查文本内容是否包含Markdown特征
      if (newItem.text && this.hasMarkdownFeatures(newItem.text)) {
        console.log('检测到历史记录项包含Markdown特征，设置格式为markdown');
        const formatKey = 'markdown';
        const formatText = chrome.i18n.getMessage(formatKey);
        newItem.source = chrome.i18n.getMessage('auto_copy_source', [formatText]);
        console.log('已将历史记录项的格式设置为Markdown:', newItem.source);
      } else if (newItem.source) {
        // 检查是否包含中文字符
        if (/[\u4e00-\u9fa5]/.test(newItem.source)) {
          // 如果是中文源标签，根据内容判断类型
          if (newItem.source === '自动复制选中文本' ||
              newItem.source === '自動複製選中文本' ||
              newItem.source === '选中文字自动复制' ||
              newItem.source === '選中文字自動複製' ||
              newItem.source === '自动复制选中文本 ()' ||
              newItem.source === '自動複製選中文本 ()') {
            newItem.source = chrome.i18n.getMessage('auto_copy_text');
          } else if (newItem.source.includes('(') && newItem.source.includes(')')) {
            // 尝试提取格式信息
            const formatMatch = newItem.source.match(/\(([^)]+)\)/);
            if (formatMatch && formatMatch[1]) {
              const formatName = formatMatch[1];
              // 确定格式类型
              let formatKey = '';
              if (formatName.includes('纯文本') || formatName.includes('純文本') ||
                  formatName.toLowerCase().includes('plain text')) {
                formatKey = 'plain_text';
              } else if (formatName.toLowerCase().includes('markdown')) {
                formatKey = 'markdown';
              }

              // 检查文本内容是否包含Markdown特征
              if (!formatKey && newItem.text) {
                // 使用类的 hasMarkdownFeatures 方法检查
                const hasMarkdownSyntax = this.hasMarkdownFeatures(newItem.text);

                if (hasMarkdownSyntax) {
                  console.log('检测到文本内容包含Markdown语法特征，设置格式为markdown');
                  formatKey = 'markdown';
                }
              }

              if (formatKey) {
                // 使用国际化消息重新构建源文本
                const formatText = chrome.i18n.getMessage(formatKey);
                let sourceText = chrome.i18n.getMessage('auto_copy_source', [formatText]);

                // 检查结果是否包含未替换的占位符
                if (sourceText && sourceText.includes('($1)')) {
                  console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, formatText);
                  // 手动替换占位符
                  sourceText = sourceText.replace('($1)', `(${formatText})`);
                }

                newItem.source = sourceText;
                console.log('Setting newItem.source with format in getAll:', formatKey, formatText, sourceText);
              } else {
                // 如果无法确定格式类型，检查是否包含未替换的占位符 ($1)
                if (newItem.source.includes('($1)')) {
                  console.log('检测到未替换的占位符 ($1)，尝试修复');
                  // 默认使用纯文本格式
                  const defaultFormatKey = 'plain_text';
                  const defaultFormatText = chrome.i18n.getMessage(defaultFormatKey);
                  let sourceText = chrome.i18n.getMessage('auto_copy_source', [defaultFormatText]);

                  // 检查结果是否包含未替换的占位符
                  if (sourceText && sourceText.includes('($1)')) {
                    console.log('检测到未替换的占位符 ($1)，手动替换:', sourceText, defaultFormatText);
                    // 手动替换占位符
                    sourceText = sourceText.replace('($1)', `(${defaultFormatText})`);
                  }

                  newItem.source = sourceText;
                  console.log('修复后的源文本:', newItem.source);
                } else {
                  // 使用通用自动复制文本
                  newItem.source = chrome.i18n.getMessage('auto_copy_text');
                }
              }
            } else {
              // 如果无法提取格式信息，使用通用自动复制文本
              newItem.source = chrome.i18n.getMessage('auto_copy_text');
            }
          }
        }
      }

      return newItem;
    });

    return processedItems;
  }

  // 清空历史记录
  async clear() {
    try {
      console.log('清空所有历史记录');
      this.historyItems = [];

      // 从存储中移除
      await chrome.storage.local.remove('clipboardHistory');
      console.log('历史记录已从存储中清空');

      return true;
    } catch (error) {
      console.error('清空历史记录失败:', error);
      return false;
    }
  }

  // 删除指定ID的历史记录
  async deleteItem(id) {
    try {
      console.log('删除历史记录项，ID:', id);

      // 强制重新加载最新数据
      await this.loadHistory();

      // 查找记录
      const index = this.historyItems.findIndex(item => item.id === id);
      if (index === -1) {
        console.log('未找到ID为', id, '的历史记录');
        return false;
      }

      // 从数组中移除
      this.historyItems.splice(index, 1);

      // 保存更改 - 直接操作存储，不使用saveHistory方法
      try {
        await chrome.storage.local.set({ clipboardHistory: this.historyItems });
        console.log('历史记录项已删除并直接保存到存储');

        // 通知其他实例更新
        try {
          await chrome.runtime.sendMessage({
            action: 'clipboardHistoryUpdated',
            operation: 'delete',
            itemId: id
          });
        } catch (msgError) {
          console.log('通知其他实例失败，可能是接收方未准备好:', msgError);
        }

        return true;
      } catch (saveError) {
        console.error('保存删除操作失败:', saveError);
        return false;
      }
    } catch (error) {
      console.error('删除历史记录项失败:', error);
      return false;
    }
  }

  // 更新最大历史记录数量
  async updateMaxItems(maxItems) {
    try {
      if (!maxItems || typeof maxItems !== 'number' || maxItems < 1) {
        console.error('无效的最大历史记录数量:', maxItems);
        return false;
      }

      this.maxHistoryItems = maxItems;
      await chrome.storage.sync.set({ maxHistoryItems: maxItems });

      // 如果当前历史记录数量超过新的限制，则裁剪
      if (this.historyItems.length > this.maxHistoryItems) {
        console.log('历史记录超出新限制，裁剪至', this.maxHistoryItems, '条');
        this.historyItems = this.historyItems.slice(0, this.maxHistoryItems);
        await this.saveHistory();
      }

      console.log('最大历史记录数量已更新为:', maxItems);
      return true;
    } catch (error) {
      console.error('更新最大历史记录数量失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const clipboardHistory = new ClipboardHistory();
