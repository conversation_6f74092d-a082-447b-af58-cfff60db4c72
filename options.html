<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SmartCopy Settings</title>
  <link rel="stylesheet" href="options.css">
  <link rel="stylesheet" href="shortcuts.css">
  <link rel="stylesheet" href="custom-dialog.css">
</head>
<body>
  <div class="container">
    <header>
      <h1 id="options-title"></h1>
    </header>

    <main>
      <section class="settings-section">
        <h2 id="copy-format-section-title"></h2>

        <div class="form-group">
          <label for="copy-format" id="copy-format-label"></label>
          <select id="copy-format">
            <!-- 格式选项将通过JavaScript动态添加 -->
          </select>
          <p class="description" data-i18n="default_copy_format_desc"></p>
        </div>
      </section>

      <section class="settings-section">
        <h2 id="auto-copy-section-title"></h2>

        <div class="form-group">
          <label class="toggle-label">
            <span id="auto-copy-label"></span>
            <div class="toggle-switch">
              <input type="checkbox" id="auto-copy-toggle">
              <span class="toggle-slider"></span>
            </div>
          </label>
          <p class="description" data-i18n="auto_copy_desc"></p>
        </div>

        <div class="form-group">
          <label for="min-characters" id="min-characters-label"></label>
          <input type="number" id="min-characters" min="1" max="100">
          <p class="description" id="min-characters-desc"></p>
        </div>

        <div class="form-group">
          <label for="copy-text-format" id="copy-text-format-label"></label>
          <select id="copy-text-format">
            <!-- 选项将通过i18n.js动态添加 -->
          </select>
          <p class="description" id="copy-text-format-desc"></p>
        </div>
      </section>

      <section class="settings-section clipboard-history-section">
        <h2>
          <span class="clipboard-history-icon">📋</span>
          <span id="clipboard-history-section-title"></span>
        </h2>
        <p class="description" id="clipboard-history-desc"></p>

        <div class="form-group">
          <label for="max-history-items" id="max-history-items-label"></label>
          <input type="number" id="max-history-items" min="1" max="500">
          <p class="description" id="max-history-items-desc"></p>
        </div>

        <div class="form-group">
          <a id="open-clipboard-history" class="btn primary clipboard-history-btn" href="clipboard-history.html" target="_blank">
            <span class="clipboard-history-icon">🔍</span>
            <span id="view-clipboard-history"></span>
          </a>
          <p class="description" id="view-clipboard-history-desc"></p>
        </div>


      </section>

      <section class="settings-section">
        <h2 id="language-settings-title"></h2>

        <div class="form-group">
          <label for="language-select" id="language-label"></label>
          <select id="language-select">
            <!-- 选项将通过i18n.js动态添加 -->
          </select>
          <p class="description" id="language-desc"></p>
        </div>
      </section>

      <section class="settings-section">
        <h2 data-i18n="shortcuts_title"></h2>
        <p class="description" data-i18n="shortcuts_desc"></p>

        <div class="shortcuts-container">
          <h3 class="shortcuts-heading" data-i18n="shortcuts_method"></h3>
          <div class="shortcut-row">
            <div class="shortcut-label" data-i18n="copy_current_tab"></div>
            <div class="shortcut-combo">
              <span class="windows">
                <span class="key">Alt</span>
                <span class="key-plus">+</span>
                <span class="key">1</span>
                <span class="os-tag">(Windows)</span>
              </span>
              <span class="mac">
                <span class="key">Option</span>
                <span class="key-plus">+</span>
                <span class="key">1</span>
                <span class="os-tag">(Mac)</span>
              </span>
            </div>
          </div>

          <div class="shortcut-row">
            <div class="shortcut-label" data-i18n="copy_all_tabs"></div>
            <div class="shortcut-combo">
              <span class="windows">
                <span class="key">Alt</span>
                <span class="key-plus">+</span>
                <span class="key">2</span>
                <span class="os-tag">(Windows)</span>
              </span>
              <span class="mac">
                <span class="key">Option</span>
                <span class="key-plus">+</span>
                <span class="key">2</span>
                <span class="os-tag">(Mac)</span>
              </span>
            </div>
          </div>

          <div class="shortcut-row">
            <div class="shortcut-label" data-i18n="auto_copy_text"></div>
            <div class="shortcut-combo">
              <span class="windows">
                <span class="key">Alt</span>
                <span class="key-plus">+</span>
                <span class="key">3</span>
                <span class="os-tag">(Windows)</span>
              </span>
              <span class="mac">
                <span class="key">Option</span>
                <span class="key-plus">+</span>
                <span class="key">3</span>
                <span class="os-tag">(Mac)</span>
              </span>
            </div>
          </div>

          <div class="shortcut-row">
            <div class="shortcut-label" data-i18n="clipboard_history"></div>
            <div class="shortcut-combo">
              <span class="windows">
                <span class="key">Alt</span>
                <span class="key-plus">+</span>
                <span class="key">4</span>
                <span class="os-tag">(Windows)</span>
              </span>
              <span class="mac">
                <span class="key">Option</span>
                <span class="key-plus">+</span>
                <span class="key">4</span>
                <span class="os-tag">(Mac)</span>
              </span>
            </div>
          </div>
        </div>

        <p class="description" data-i18n="shortcuts_note"></p>
      </section>

    </main>

    <footer>
      <div class="buttons">
        <button id="save-btn" class="btn primary"></button>
        <button id="reset-btn" class="btn secondary"></button>
      </div>
      <div class="status" id="status-message"></div>
    </footer>
  </div>

  <script src="i18n.js"></script>
  <script src="custom-dialog.js"></script>
  <script type="module" src="options.js"></script>
</body>
</html>
