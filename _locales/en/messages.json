{"extension_name": {"message": "SmartCopy All-in-One Copy Tool", "description": "The name of the extension"}, "extension_description": {"message": "One-click to bypass webpage copy restrictions, Smart copy of website tab. with auto-copy of selected text and super copy features.", "description": "The description of the extension"}, "popup_title": {"message": "SmartCopy", "description": "Title of the popup window"}, "default_copy_format": {"message": "Tab Default Copy Format:", "description": "Label for default copy format selector"}, "default_copy_format_desc": {"message": "Choose the format to use when copying tab information", "description": "Description for default copy format selector"}, "copy_current_tab": {"message": "Copy Current Tab", "description": "Button to copy current tab"}, "copy_all_tabs": {"message": "Copy All Tabs", "description": "Button to copy all tabs"}, "clipboard_history": {"message": "Clipboard History", "description": "Button to open clipboard history"}, "auto_copy_desc": {"message": "Automatically copy text when selected on any webpage", "description": "Description for auto copy feature"}, "super_copy": {"message": "Super Copy", "description": "Label for super copy toggle"}, "advanced_settings": {"message": "Super Copy Settings", "description": "Title for super copy settings section"}, "copy_failed": {"message": "Copy failed: $ERROR$", "description": "Error message when copy fails", "placeholders": {"error": {"content": "$1", "example": "Permission denied"}}}, "no_tabs_found": {"message": "No tabs found", "description": "Error message when no tabs are found"}, "no_text_to_copy": {"message": "No text to copy", "description": "Error message when there is no text to copy"}, "super_copy_enabled": {"message": "Super Copy enabled", "description": "Notification when Super Copy is enabled"}, "super_copy_disabled": {"message": "Super Copy disabled", "description": "Notification when <PERSON> Copy is disabled"}, "settings_saved": {"message": "Setting<PERSON> saved", "description": "Success message when settings are saved"}, "settings_reset": {"message": "Settings reset to defaults", "description": "Success message when settings are reset"}, "settings_save_failed": {"message": "Failed to save settings, please try again", "description": "Error message when settings save fails"}, "plain_text": {"message": "Plain Text", "description": "Option for plain text format"}, "markdown": {"message": "<PERSON><PERSON>", "description": "Option for markdown format"}, "format_default": {"message": "[Title] URL", "description": "Default format description"}, "format_title_only": {"message": "[Title]", "description": "Title only format description"}, "format_url_only": {"message": "URL", "description": "URL only format description"}, "format_title_dash_url": {"message": "Title - URL", "description": "Title dash URL format description"}, "format_markdown": {"message": "<PERSON><PERSON>", "description": "Markdown format description"}, "format_html": {"message": "HTML", "description": "HTML format description"}, "format_csv": {"message": "CSV", "description": "CSV format description"}, "format_json": {"message": "JSON", "description": "JSON format description"}, "format_html_table": {"message": "HTML Table", "description": "HTML table format description"}, "format_default_desc": {"message": "Default format", "description": "Description for default format"}, "format_title_only_desc": {"message": "Title only", "description": "Description for title only format"}, "format_url_only_desc": {"message": "URL only", "description": "Description for URL only format"}, "format_title_dash_url_desc": {"message": "Title and URL, separated by dash", "description": "Description for title dash URL format"}, "format_markdown_desc": {"message": "Markdown link format", "description": "Description for Markdown format"}, "format_html_desc": {"message": "HTML link format", "description": "Description for HTML format"}, "format_csv_desc": {"message": "CSV format, for spreadsheets", "description": "Description for CSV format"}, "format_json_desc": {"message": "JSON format", "description": "Description for JSON format"}, "format_html_table_desc": {"message": "HTML table format", "description": "Description for HTML table format"}, "options_title": {"message": "SmartCopy Settings", "description": "Title of the options page"}, "copy_format_settings": {"message": "Tab Copy Format Settings", "description": "Title for copy format settings section"}, "auto_copy_settings": {"message": "Auto Copy Settings", "description": "Title for auto copy settings section"}, "enable_auto_copy": {"message": "Enable Auto Copy Selected Text", "description": "Label for enable auto copy toggle"}, "min_characters": {"message": "Minimum Characters:", "description": "Label for minimum characters input"}, "min_characters_desc": {"message": "Minimum number of characters to trigger auto copy (to avoid accidental triggers)", "description": "Description for minimum characters input"}, "copy_text_format": {"message": "Selected Text Copy Format:", "description": "Label for copy text format selector"}, "copy_text_format_desc": {"message": "Choose the format to use when auto copying selected text", "description": "Description for copy text format selector"}, "clipboard_history_section": {"message": "Clipboard History", "description": "Title for clipboard history section"}, "clipboard_history_desc": {"message": "The extension automatically saves all copied content, including tab information and selected text", "description": "Description for clipboard history"}, "max_history_items": {"message": "Maximum History Items:", "description": "Label for maximum history items input"}, "max_history_items_desc": {"message": "Set the maximum number of history items to save (default is 10)", "description": "Description for maximum history items input"}, "view_clipboard_history": {"message": "View Clipboard History", "description": "Button to view clipboard history"}, "view_clipboard_history_desc": {"message": "Open the history page to view, copy, and manage your copy history", "description": "Description for view clipboard history button"}, "language_settings": {"message": "Language Settings", "description": "Title for language settings section"}, "language": {"message": "Language:", "description": "Label for language selector"}, "language_desc": {"message": "Choose the language for the extension interface", "description": "Description for language selector"}, "language_auto": {"message": "Auto (Browser Default)", "description": "Option for auto language detection"}, "language_en": {"message": "English", "description": "Option for English language"}, "language_zh_CN": {"message": "Simplified Chinese", "description": "Option for Simplified Chinese language"}, "language_zh_TW": {"message": "Traditional Chinese", "description": "Option for Traditional Chinese language"}, "save_settings": {"message": "Save Settings", "description": "Button to save settings"}, "reset_defaults": {"message": "Reset to Defaults", "description": "Button to reset settings to defaults"}, "clipboard_history_title": {"message": "Clipboard History", "description": "Title of the clipboard history page"}, "refresh": {"message": "Refresh", "description": "Button to refresh clipboard history"}, "clear_history": {"message": "Clear History", "description": "Button to clear clipboard history"}, "items_per_page": {"message": "Items per page:", "description": "Label for items per page selector"}, "empty_history": {"message": "No clipboard history yet", "description": "Message when clipboard history is empty"}, "empty_history_desc": {"message": "When you copy content, records will be automatically saved here", "description": "Description for empty clipboard history"}, "loading": {"message": "Loading history records...", "description": "Message when loading clipboard history"}, "prev_page": {"message": "< Previous", "description": "Button to go to previous page"}, "next_page": {"message": "Next >", "description": "Button to go to next page"}, "page_info": {"message": "Page $CURRENT$ of $TOTAL$", "description": "Page information", "placeholders": {"current": {"content": "$1", "example": "1"}, "total": {"content": "$2", "example": "5"}}}, "title_column": {"message": "Title", "description": "Title column in HTML table"}, "url_column": {"message": "URL", "description": "URL column in HTML table"}, "expand": {"message": "Expand", "description": "Button to expand content"}, "collapse": {"message": "Collapse", "description": "Button to collapse content"}, "copy": {"message": "Copy", "description": "Button to copy content"}, "delete": {"message": "Delete", "description": "Button to delete content"}, "history_refreshed": {"message": "History refreshed", "description": "Message when history is refreshed"}, "refresh_failed": {"message": "Failed to refresh history, please try again", "description": "Error message when refresh fails"}, "confirm_clear_history": {"message": "Are you sure you want to clear all clipboard history? This action cannot be undone.", "description": "Confirmation message for clearing history"}, "extension_prompt_clear_history": {"message": "Are you sure you want to clear all clipboard history? This action cannot be undone.", "description": "Extension prompt for clearing clipboard history"}, "history_cleared": {"message": "All history records cleared", "description": "Message when history is cleared"}, "clear_failed": {"message": "Failed to clear history, please try again", "description": "Error message when clear fails"}, "item_deleted": {"message": "Record deleted", "description": "Message when an item is deleted"}, "delete_failed": {"message": "Failed to delete, please try again", "description": "Error message when delete fails"}, "confirm_reset_defaults": {"message": "Are you sure you want to reset all settings to default values?", "description": "Confirmation message for resetting settings"}, "extension_prompt_reset_defaults": {"message": "Are you sure you want to reset all settings to default values?", "description": "Extension prompt for resetting settings to defaults"}, "confirm": {"message": "Confirm", "description": "Confirm button text"}, "cancel": {"message": "Cancel", "description": "Cancel button text"}, "language_changed": {"message": "Language changed", "description": "Message when language is changed"}, "shortcuts_title": {"message": "Keyboard Shortcuts", "description": "Title for shortcuts section"}, "shortcuts_desc": {"message": "The following shortcuts are configured by default and can be used on any webpage", "description": "Description for shortcuts section"}, "shortcuts_note": {"message": "Note: You can also adjust these in Chrome settings as needed", "description": "Note about adjusting shortcuts"}, "shortcuts_method": {"message": "Keyboard Shortcuts", "description": "Title for the keyboard shortcuts section"}, "super_copy_unavailable": {"message": "Super Copy (Not available on this page)", "description": "Message shown when super copy is not available on the current page"}, "cannot_get_current_tab": {"message": "Cannot get current tab", "description": "Error message when current tab cannot be retrieved"}, "floating_button_title": {"message": "Click to toggle Super Copy, drag to adjust position", "description": "Title/tooltip for the floating button"}, "floating_button_text": {"message": "Super<br><PERSON><PERSON>", "description": "Text displayed on the floating button"}, "floating_button_enabled": {"message": "Super Copy enabled, click to disable", "description": "Title/tooltip for the floating button when Super Copy is enabled"}, "floating_button_disabled": {"message": "Super Copy disabled, click to enable", "description": "Title/tooltip for the floating button when Super Copy is disabled"}, "super_copy_enabled_notification": {"message": "Super Copy enabled", "description": "Notification shown when Super Copy is enabled"}, "super_copy_disabled_notification": {"message": "Super Copy disabled", "description": "Notification shown when <PERSON> Copy is disabled"}, "show_floating_button": {"message": "Show floating button on pages", "description": "Label for floating button display toggle"}, "show_floating_button_desc": {"message": "Display the Super Copy floating button on web pages, You can drag the button to adjust its position.", "description": "Description for floating button display setting"}, "super_copy_init_failed": {"message": "Super Copy initialization failed: $ERROR$", "description": "Error message when Super Copy initialization fails", "placeholders": {"error": {"content": "$1", "example": "Permission denied"}}}, "site_excluded": {"message": "Site $SITE$ is excluded, Super Copy not enabled", "description": "Message shown when a site is excluded from Super Copy", "placeholders": {"site": {"content": "$1", "example": "example.com"}}}, "text_copied": {"message": "Text copied successfully", "description": "Notification shown when text is copied"}, "added_to_multi_selection": {"message": "Added to multi-selection ($1 segments)", "description": "Notification shown when text is added to multi-selection", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "storage_cleared": {"message": "All extension storage cleared", "description": "Message shown when all extension storage is cleared"}, "storage_clear_failed": {"message": "Failed to clear extension storage", "description": "Error message when clearing extension storage fails"}, "copied_chars_format": {"message": "Copied $COUNT$ characters ($FORMAT$)", "description": "Message shown when text is copied with character count and format", "placeholders": {"count": {"content": "$1", "example": "9"}, "format": {"content": "$2", "example": "Plain Text"}}}, "copied_chars_template": {"message": "Copied {count} characters ({format})", "description": "Template for message shown when text is copied with character count and format"}, "plain_text_format": {"message": "Plain Text", "description": "Name of plain text format"}, "markdown_format": {"message": "<PERSON><PERSON>", "description": "Name of Markdown format"}, "copy_text_format_updated": {"message": "Selected text copy format updated to: {format}", "description": "Message shown when selected text copy format is updated"}, "tab_format_mismatch": {"message": "Tab copy format mismatch detected after page load, forcing UI update", "description": "Warning message when tab copy format doesn't match"}, "text_format_mismatch": {"message": "Selected text copy format mismatch detected after page load, forcing UI update", "description": "Warning message when selected text copy format doesn't match"}, "initialization_failed": {"message": "Initialization failed, please refresh the page and try again", "description": "Error message when initialization fails"}, "copy_format_updated": {"message": "Default copy format updated to: {format}", "description": "Message shown when default copy format is updated"}, "min_characters_updated": {"message": "Minimum characters updated to: {value}", "description": "Message shown when minimum characters setting is updated"}, "max_history_updated": {"message": "Maximum history items updated to: {value}", "description": "Message shown when maximum history items setting is updated"}, "operation_too_frequent": {"message": "Operation too frequent, please wait a moment and try again", "description": "Message shown when operations are too frequent"}, "copy_failed_with_error": {"message": "Copy failed: $ERROR$", "description": "Error message when copy fails with error details", "placeholders": {"error": {"content": "$1", "example": "Permission denied"}}}, "copied_current_tab": {"message": "Current tab copied", "description": "Message shown when current tab is copied"}, "copied_all_tabs": {"message": "Copied $COUNT$ tabs", "description": "Message shown when all tabs are copied", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "copying_current_tab": {"message": "Copying current tab...", "description": "Message shown when copying current tab"}, "copying_all_tabs": {"message": "Copying all tabs...", "description": "Message shown when copying all tabs"}, "toggling_auto_copy": {"message": "Toggling auto copy...", "description": "Message shown when toggling auto copy"}, "opening_clipboard_history": {"message": "Opening clipboard history...", "description": "Message shown when opening clipboard history"}, "auto_copy_enabled": {"message": "Auto copy enabled", "description": "Message shown when auto copy is enabled"}, "auto_copy_disabled": {"message": "Auto copy disabled", "description": "Message shown when auto copy is disabled"}, "toggle_failed": {"message": "Toggle failed: $ERROR$", "description": "Message shown when toggling fails", "placeholders": {"error": {"content": "$1", "example": "Unknown error"}}}, "open_clipboard_history_failed": {"message": "Failed to open clipboard history: $ERROR$", "description": "Message shown when opening clipboard history fails", "placeholders": {"error": {"content": "$1", "example": "Unknown error"}}}, "copied_to_clipboard": {"message": "Copied to clipboard", "description": "Message shown when text is copied to clipboard"}, "copy_all_tabs_action": {"message": "Copy all tabs", "description": "Action name for copying all tabs"}, "super_copy_enabled_with_details": {"message": "Super Copy enabled, you can now freely copy content", "description": "Detailed message shown when Super Copy is enabled"}, "super_copy_toggle_error": {"message": "Error toggling Super Copy: $ERROR$", "description": "Error message when toggling Super Copy fails", "placeholders": {"error": {"content": "$1", "example": "Unknown error"}}}, "shortcut": {"message": "Shortcut", "description": "Label for keyboard shortcut"}, "auto_copy_source": {"message": "Auto-copied text ($1)", "description": "Source label for auto-copied text in clipboard history"}, "manual_copy": {"message": "Manual copy", "description": "Label for manually copied text in clipboard history"}, "auto_copy_text": {"message": "Auto-copied text", "description": "Label for auto-copied text in clipboard history"}, "dialog_title_clear_history": {"message": "Clear Clipboard History", "description": "Dialog title for clearing clipboard history"}, "dialog_title_reset_settings": {"message": "Reset Settings", "description": "Dialog title for resetting settings"}}