// --- 全局变量声明必须最前面 ---
let settings = {
  autoTextCopy: true,
  minCharacters: 5,
  copyTextFormat: 'plain' // 默认为纯文本
};

// 从存储中加载设置
function loadInitialSettings() {
  console.log('正在从存储加载初始设置...');

  // 获取当前浏览器UI语言
  const browserUILanguage = chrome.i18n.getUILanguage();
  console.log('Browser UI language:', browserUILanguage);

  // 获取当前语言设置
  chrome.storage.sync.get({
    autoTextCopy: true,
    minCharacters: 5,
    copyTextFormat: 'plain',
    language: 'auto'
  }, function(result) {
    console.log('已加载设置:', result);

    // 特别检查 copyTextFormat 设置
    if (result.copyTextFormat) {
      console.log('检测到选中文字复制格式设置:', result.copyTextFormat);
    } else {
      console.warn('未检测到选中文字复制格式设置，使用默认值: plain');
      result.copyTextFormat = 'plain';

      // 保存默认值到存储
      chrome.storage.sync.set({ copyTextFormat: 'plain' });
    }

    settings = {
      ...settings,
      ...result
    };

    // 确定实际使用的语言
    let displayLanguage = settings.language || 'auto';
    if (displayLanguage === 'auto') {
      // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
      const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
      console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

      if (browserUILanguage.startsWith('zh') || rawNavigatorLanguage.startsWith('zh')) {
        if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO') ||
            rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
          displayLanguage = 'zh_TW';
        } else {
          displayLanguage = 'zh_CN';
        }
      } else {
        // 对于所有非中文语言，强制使用英语
        displayLanguage = 'en';
      }
    }
    console.log('实际使用的语言:', displayLanguage, 'Chrome API 浏览器语言:', browserUILanguage, '原始浏览器语言:', navigator.language);

    console.log('合并后的设置:', settings);

    // 加载设置后初始化自动复制功能
    if (settings.autoTextCopy) {
      document.removeEventListener('mouseup', handleTextSelection);
      document.addEventListener('mouseup', handleTextSelection);
      console.log('已启用自动复制功能');
    }
  });
}

// 在DOM内容加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', loadInitialSettings);
} else {
  loadInitialSettings();
}

// 从存储中加载设置
async function loadSettings() {
  try {
    const storedSettings = await chrome.storage.sync.get({
      autoTextCopy: true,
      minCharacters: 5,
      copyTextFormat: 'plain'
    });

    // 特别检查 copyTextFormat 设置
    if (storedSettings.copyTextFormat) {
      console.log('检测到选中文字复制格式设置:', storedSettings.copyTextFormat);
    } else {
      console.warn('未检测到选中文字复制格式设置，使用默认值: plain');
      storedSettings.copyTextFormat = 'plain';

      // 保存默认值到存储
      await chrome.storage.sync.set({ copyTextFormat: 'plain' });

      // 验证设置是否成功保存
      const verifySettings = await chrome.storage.sync.get(['copyTextFormat']);
      console.log('验证保存的默认选中文字复制格式:', verifySettings.copyTextFormat);

      // 确保使用实际保存的值
      storedSettings.copyTextFormat = verifySettings.copyTextFormat;
    }

    settings = {
      ...settings,
      ...storedSettings
    };

    // 强制再次保存设置，确保设置被持久化
    await chrome.storage.sync.set({ copyTextFormat: settings.copyTextFormat });
    console.log('已强制保存选中文字复制格式设置:', settings.copyTextFormat);

    console.log('已从存储加载设置:', settings);
    return settings;
  } catch (error) {
    console.error('加载设置失败:', error);
    return settings;
  }
}

// 初始化函数
async function initialize() {
  // 加载设置
  await loadSettings();
  console.log('设置加载完成，当前最小字符数:', settings.minCharacters);

  // 初始化自动复制功能
  if (settings.autoTextCopy) {
    document.removeEventListener('mouseup', handleTextSelection);
    document.addEventListener('mouseup', handleTextSelection);
    setupSelectionChangeHandler();
  }
}

// 设置selectionchange事件处理
function setupSelectionChangeHandler() {
  if (!window._autoCopySelectionChangeHandler) {
    let lastSelection = '';
    let selectionDebounceTimer = null;

    window._autoCopySelectionChangeHandler = function() {
      if (!settings.autoTextCopy) return;

      // 如果鼠标按下状态，说明用户正在选择文本，暂不处理
      if (isMouseDown) return;

      const selection = window.getSelection();
      if (!selection) return;

      const selectedText = selection.toString().trim();
      const active = document.activeElement;

      if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) return;

      // 检查是否与点击前的选择相同（用于判断是否是点击页面而非新选择）
      const isSameAsBeforeClick = selectedText === window._lastSelectionBeforeClick;

      // 如果是点击页面而非新选择，则不进行复制
      if (isSameAsBeforeClick) {
        console.log('selectionchange: 检测到点击页面而非新选择，跳过复制操作');
        return;
      }

      if (selectedText && selectedText.length >= settings.minCharacters) {
        // 检查是否与最近复制的文本相同
        if (selectedText === lastCopiedText) {
          // 如果是相同文本，检查是否是由于用户点击页面导致的重复复制
          const timeSinceLastCopy = Date.now() - lastCopyTime;
          if (timeSinceLastCopy < 1000) { // 1秒内的重复复制视为重复操作
            console.log('setupSelectionChangeHandler: 检测到短时间内重复复制相同文本，跳过复制操作');
            return;
          }
        }

        // 只有当文本与上次选择不同时才更新lastSelection并复制
        if (selectedText !== lastSelection) {
          console.log(`选中文本长度: ${selectedText.length}, 最小字符数设置: ${settings.minCharacters}`);
          lastSelection = selectedText;

          // 延迟处理，避免在多段选择过程中多次触发
          if (mouseUpDelayTimer) {
            clearTimeout(mouseUpDelayTimer);
          }

          mouseUpDelayTimer = setTimeout(() => {
            copyTextToClipboard(selectedText);
            mouseUpDelayTimer = null;
          }, 100);
        }
      }
    };

    window._autoCopySelectionChangeDebounced = function() {
      if (selectionDebounceTimer) clearTimeout(selectionDebounceTimer);
      selectionDebounceTimer = setTimeout(window._autoCopySelectionChangeHandler, 200);
    };
  }

  document.removeEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
  document.addEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
  console.log('已设置selectionchange事件处理');
}

// 在DOM内容加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

// 确保在内容脚本加载时立即加载设置
loadSettings().then(() => {
  console.log('设置加载完成，当前最小字符数:', settings.minCharacters);
  // 初始化自动复制功能
  initAutoTextCopyListeners();
});

// 获取标准化的域名（仅返回主域名，使超级复制设置在同一网站所有页面共享）
function getDomain() {
  const host = window.location.hostname;

  // 提取主域名
  // 处理常见的情况，如 www.example.com, sub.example.com 等
  const domainRegex = /([\w-]+\.)?([\w-]+\.[\w-]+)$/;
  const matches = host.match(domainRegex);

  if (matches && matches[2]) {
    // 返回主域名，如 example.com
    logger.debug('超级复制', `提取主域名: ${matches[2]} (原域名: ${host})`);
    return matches[2];
  }

  // 如果无法提取，返回原始域名
  logger.debug('超级复制', `无法提取主域名，使用原始域名: ${host}`);
  return host;
}

let superCopyEnabled = false;

// 注意: getSuperCopyEnabled 函数已经被移除，直接在 initSuperCopyPerSite 函数中获取设置

// 内容脚本 - 统一事件处理以绕过复制/选择/右键限制
// ========== 超级复制事件处理函数 ==========
function superCopyContextMenuHandler(e) {
  // 检查超级复制是否启用
  if (!superCopyEnabled) {
    logger.debug('超级复制', `超级复制已禁用，不拦截右键菜单事件`);
    return;
  }

  // 检查事件目标是否是浮动按钮或其子元素
  const floatingBtn = document.getElementById('smartcopy-floating-btn');
  if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
    // 如果是浮动按钮，不拦截事件
    logger.debug('超级复制', `检测到浮动按钮的右键菜单事件，不拦截`);
    return;
  }

  logger.debug('超级复制', `拦截右键菜单事件`);
  e.stopImmediatePropagation();
}

// 只拦截Ctrl/Meta+C，Alt数字等不拦截
function superCopyKeydownHandler(e) {
  // 检查超级复制是否启用
  if (!superCopyEnabled) {
    return;
  }

  // 检查事件目标是否是浮动按钮或其子元素
  const floatingBtn = document.getElementById('smartcopy-floating-btn');
  if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
    // 如果是浮动按钮，不拦截事件
    logger.debug('超级复制', `检测到浮动按钮的键盘事件，不拦截`);
    return;
  }

  if ((e.ctrlKey || e.metaKey) && (e.key.toLowerCase() === 'c' || e.keyCode === 67)) {
    logger.debug('超级复制', `拦截复制快捷键事件`);
    e.stopImmediatePropagation();
    return false;
  }
}

function superCopyCopyHandler(e) {
  // 检查超级复制是否启用
  if (!superCopyEnabled) {
    logger.debug('超级复制', `超级复制已禁用，不拦截复制事件`);
    return;
  }

  // 检查事件目标是否是浮动按钮或其子元素
  const floatingBtn = document.getElementById('smartcopy-floating-btn');
  if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
    // 如果是浮动按钮，不拦截事件
    logger.debug('超级复制', `检测到浮动按钮的复制事件，不拦截`);
    return;
  }

  logger.debug('超级复制', `拦截复制事件并处理`);
  e.stopImmediatePropagation();
  e.preventDefault();
  const text = window.getSelection().toString();
  if (e.clipboardData) {
    e.clipboardData.setData('text/plain', text);
  } else if (window.clipboardData) {
    window.clipboardData.setData('Text', text);
  }
}

// ========== 动态注册/注销超级复制相关事件 ==========
function registerSuperCopyEvents() {
  // 先清除可能存在的事件处理程序，确保不会重复注册
  unregisterSuperCopyEvents();

  // 注册事件处理程序
  logger.debug('超级复制', `注册超级复制事件处理程序`);
  document.addEventListener('contextmenu', superCopyContextMenuHandler, true);
  document.addEventListener('keydown', superCopyKeydownHandler, true);
  document.addEventListener('copy', superCopyCopyHandler, true);

  // 标记已注册状态
  window._superCopyEventsRegistered = true;

  // 执行全局事件拦截
  handleGlobalEventInterception();

  // 注册 CSP 兼容解锁方案
  registerCSPCompatibleUnlock();

  // 添加强制允许选中的样式
  addUserSelectStyle();
}

function unregisterSuperCopyEvents() {
  logger.debug('超级复制', `注销超级复制事件处理程序`);

  // 移除事件监听器
  try {
    document.removeEventListener('contextmenu', superCopyContextMenuHandler, true);
    document.removeEventListener('keydown', superCopyKeydownHandler, true);
    document.removeEventListener('copy', superCopyCopyHandler, true);

    // 标记未注册状态
    window._superCopyEventsRegistered = false;

    // 移除全局事件拦截器
    removeGlobalEventInterception();

    // 移除 CSP 兼容解锁方案
    removeCSPCompatibleUnlock();

    // 移除强制允许选中的样式
    removeUserSelectStyle();

    logger.debug('超级复制', `事件处理程序已成功注销`);
  } catch (error) {
    logger.error('超级复制', `注销事件处理程序时出错:`, error);
  }
}

// 记忆存储 - 用于记住已破解的网站
const unblockMemory = {
  // 存储已破解的网站
  sites: new Set(),

  // 添加网站到记忆
  add(url) {
    const hostname = new URL(url).hostname;
    this.sites.add(hostname);
    this.saveToStorage();
  },

  // 检查网站是否在记忆中
  has(url) {
    const hostname = new URL(url).hostname;
    return this.sites.has(hostname);
  },

  // 保存到存储
  async saveToStorage() {
    try {
      await chrome.storage.local.set({
        unblockSites: Array.from(this.sites)
      });
    } catch (err) {
      console.error('保存解锁网站记忆失败:', err);
    }
  },

  // 从存储加载
  async loadFromStorage() {
    try {
      const data = await chrome.storage.local.get('unblockSites');
      if (data.unblockSites && Array.isArray(data.unblockSites)) {
        this.sites = new Set(data.unblockSites);
      }
    } catch (err) {
      console.error('加载解锁网站记忆失败:', err);
    }
  }
};

// 粘贴控制
let isPasteHandled = false;
let pasteTimeout = null;

// 初始化时添加全局事件监听器，不依赖于superCopyEnabled状态
document.addEventListener('paste', function(e) {
  // 如果超级复制未启用，则不拦截粘贴
  if (!superCopyEnabled) {
    return true;
  }

  // 如果粘贴已被处理，则阻止所有其他粘贴事件
  if (isPasteHandled) {
    e.stopImmediatePropagation();
    e.preventDefault();
    return false;
  }

  // 否则交给handlePaste处理
  return handlePaste(e, '通用网站');
}, true);

// 创建一个统一的粘贴处理函数
function handlePaste(event, siteType) {
  // 如果超级复制未启用，则不处理粘贴
  if (!superCopyEnabled) {
    return true;
  }

  // 阻止事件传播和默认行为
  if (event) {
    event.stopImmediatePropagation();
    event.preventDefault(); // 阻止浏览器默认粘贴行为
  }

  // 如果已经处理过粘贴，则跳过
  if (isPasteHandled) {
    logger.debug('粘贴处理', `[${siteType}] 粘贴已处理，跳过重复处理`);
    return false;
  }

  // 设置标记，防止重复处理
  isPasteHandled = true;

  // 清除之前的超时
  if (pasteTimeout) {
    clearTimeout(pasteTimeout);
  }

  // 设置新的超时，确保标记会被重置
  pasteTimeout = setTimeout(() => {
    isPasteHandled = false;
    logger.debug('粘贴处理', `[${siteType}] 粘贴处理标记已重置`);
  }, 500);

  // 获取当前焦点元素
  const activeElement = document.activeElement;

  // 如果没有焦点元素，则不处理
  if (!activeElement) {
    isPasteHandled = false;
    return false;
  }

  // 记录原始状态（不设置contenteditable，避免触发浏览器原生粘贴）
  const wasContentEditable = activeElement.hasAttribute('contenteditable');
  const originalContentEditable = activeElement.getAttribute('contenteditable');

  try {
    // 尝试读取剪贴板并手动执行粘贴
    navigator.clipboard.readText().then(text => {
      if (!text) {
        isPasteHandled = false;
        return;
      }

      // 根据元素类型处理粘贴
      if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
        // 处理输入框 - 直接修改value
        const startPos = activeElement.selectionStart || 0;
        const endPos = activeElement.selectionEnd || startPos;
        const beforeText = activeElement.value.substring(0, startPos);
        const afterText = activeElement.value.substring(endPos);

        // 直接插入文本，不等待浏览器原生粘贴
        activeElement.value = beforeText + text + afterText;
        activeElement.selectionStart = activeElement.selectionEnd = startPos + text.length;
        logger.debug('粘贴处理', `[${siteType}] 已手动处理输入框粘贴`);
      } else if (activeElement.isContentEditable || siteType === '快捷键') {
        // 对于可编辑元素，使用DOM API插入文本
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const textNode = document.createTextNode(text);
          range.deleteContents(); // 删除选中内容
          range.insertNode(textNode);
          range.setStartAfter(textNode);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
          logger.debug('粘贴处理', `[${siteType}] 已手动处理可编辑元素粘贴`);
        }
      } else {
        // 对于其他元素，创建一个可见的span元素
        const span = document.createElement('span');
        span.textContent = text;
        span.style.display = 'inline';
        span.style.color = 'inherit';
        span.style.backgroundColor = 'transparent';
        span.style.opacity = '1';
        span.style.visibility = 'visible';

        // 尝试插入到当前位置
        activeElement.appendChild(span);
        logger.debug('粘贴处理', `[${siteType}] 已插入span元素显示粘贴内容`);
      }
    }).catch(err => {
      console.warn(`[${siteType}] 读取剪贴板失败`, err);
      isPasteHandled = false;
    });
  } catch (e) {
    console.warn(`[${siteType}] 粘贴处理失败`, e);
    isPasteHandled = false;
  }

  return false; // 返回false阻止默认行为
}

    // --- Constants and Globals ---
    const excludedSites = []; // Define the missing constant

    // 使用 Chrome 存储 API 替代 GM_getValue
    // superCopyEnabled 已在文件顶部声明，这里无需再次声明，直接赋值即可
    superCopyEnabled = false; // 默认值
    let currentFormat = 'markdown'; // 默认值

    // 从 Chrome 存储中加载初始状态
    chrome.storage.local.get(['superCopyEnabled', 'copyFormat'], function(result) {
        if (result.superCopyEnabled !== undefined) {
            superCopyEnabled = result.superCopyEnabled;
            logger.debug('初始化', '已从存储加载超级复制状态:', superCopyEnabled);

            // 如果超级复制已启用，则在加载后应用
            if (superCopyEnabled) {
                setTimeout(applySuperCopy, 500); // 延迟应用，确保 DOM 已准备好
            }
        }
        if (result.copyFormat !== undefined) {
            currentFormat = result.copyFormat;
            logger.debug('初始化', '已从存储加载复制格式:', currentFormat);
        }
    })
    let copyCounter = 0;
    const MAX_HISTORY_SIZE = 10;
    const COPY_HISTORY_KEY = 'copyHistory';
    const SUPER_COPY_STYLE_ID = 'enhanced-tab-copy-super-style'; // Generic ID for super copy styles
    const STATUS_DIV_ID = 'enhanced-copy-status';

    // Global references for Super Copy observer and interval
    let superCopyObserver = null;
    let superCopyReapplyInterval = null;

    // --- Helper Functions (Moved to Top Level) ---

    // Function to display status messages - 已禁用，改用红色浮动通知
    const showStatusMessage = (message, type = 'success') => {
      // 使用红色浮动通知替代绿色通知
      // 检查消息是否是 i18n 消息键
      if (message && message.startsWith && (
          message.startsWith('super_copy_') ||
          message.startsWith('site_') ||
          message.startsWith('storage_'))) {
        showCopyNotification({
          messageKey: message,
          useI18n: true,
          type: type
        });
      } else {
        showCopyNotification({
          text: message,
          useI18n: false,
          type: type
        });
      }

      // 原始代码已禁用，不再创建绿色通知
      /*
      let statusDiv = document.getElementById(STATUS_DIV_ID);
      if (!statusDiv) {
        statusDiv = document.createElement('div');
        statusDiv.id = STATUS_DIV_ID;
        statusDiv.style.position = 'fixed';
        statusDiv.style.right = '20px';
        statusDiv.style.bottom = '20px';
        statusDiv.style.padding = '10px 15px';
        statusDiv.style.borderRadius = '5px';
        statusDiv.style.color = 'white';
        statusDiv.style.fontSize = '14px';
        statusDiv.style.zIndex = '2147483647';
        statusDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        statusDiv.style.transition = 'all 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28)';
        statusDiv.style.opacity = '0';
        statusDiv.style.transform = 'translateY(20px)';
        statusDiv.style.pointerEvents = 'none';
        statusDiv.style.fontFamily = 'Arial, sans-serif';
        statusDiv.style.display = 'flex';
        statusDiv.style.alignItems = 'center';
        // Append to body if possible, otherwise documentElement
        (document.body || document.documentElement).appendChild(statusDiv);
      }

      statusDiv.textContent = message;
      statusDiv.style.backgroundColor = type === 'success' ? 'rgba(40, 167, 69, 0.9)' : 'rgba(220, 53, 69, 0.9)';

      // Show message with animation
      setTimeout(() => {
        statusDiv.style.opacity = '1';
        statusDiv.style.transform = 'translateY(0)';
      }, 10);

      // Hide message after a delay
      setTimeout(() => {
        statusDiv.style.opacity = '0';
        statusDiv.style.transform = 'translateY(20px)';
        // Optional: Remove element after transition to prevent buildup
        // Consider removing logic can be added in cleanup function instead
      }, 3000 + (message.length * 30));
      */
    };

    // Function to add strong CSS rules for Super Copy
    const addSuperCopyStyles = () => {
      let styleElement = document.getElementById(SUPER_COPY_STYLE_ID);
      if (!styleElement) {
        try {
          styleElement = document.createElement('style');
          styleElement.id = SUPER_COPY_STYLE_ID;

          // 安全地添加样式元素，确保在DOM不完整的情况下也能正常工作
          if (document.head) {
            document.head.appendChild(styleElement);
          } else if (document.documentElement) {
            document.documentElement.appendChild(styleElement);
          } else if (document.body) {
            document.body.appendChild(styleElement);
          } else {
            console.warn('[SmartCopy] 无法添加超级复制样式，DOM不完整');
            return; // 如果无法添加样式，则提前返回
          }
        } catch (error) {
          console.error('[SmartCopy] 添加样式元素时出错:', error);
          return; // 出错时提前返回
        }
      }
      // Generic CSS rules for bypassing copy protection
      styleElement.textContent = `
        /* Universal Selection */
        body, body *, ::before, ::after {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
          cursor: auto !important;
        }
        /* Override common anti-copy attributes/styles */
        [onselectstart], [oncopy], [oncut], [oncontextmenu], [ondragstart], [unselectable="on"],
        [style*="user-select"], [style*="pointer-events"] {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          user-select: text !important;
          pointer-events: auto !important; /* Allow interactions */
        }
        /* Ensure common content areas are selectable */
        article, main, .content, .article, .post, section, p, div, span, li, td, th {
            -webkit-user-select: text !important;
            user-select: text !important;
            pointer-events: auto !important;
        }
         /* Keep interactions on links/buttons */
        a, button, input, select, textarea {
            -webkit-user-select: auto !important;
            user-select: auto !important;
            pointer-events: auto !important;
            cursor: auto !important;
        }
        a[href], button, input[type="button"], input[type="submit"] {
            cursor: pointer !important;
        }
        /* Override page-specific anti-select rules */
        body *:not(input):not(textarea) {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          -khtml-user-select: text !important;
          user-select: text !important;
          pointer-events: auto !important;
          -webkit-touch-callout: default !important;
        }
        /* Exact override for selectors like 'body * :not(input):not(textarea)' */
        body * :not(input):not(textarea) {
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;
          -khtml-user-select: text !important;
          user-select: text !important;
          pointer-events: auto !important;
          -webkit-touch-callout: default !important;
        }
      `;
      logger.debug('超级复制', 'Super Copy CSS rules applied.');
    };

    // Function to remove event listeners thoroughly for Super Copy
    const removeSuperCopyEventListeners = (targetNode = document.documentElement) => {
        // 分开处理不同类型的事件
        const nonMouseEventsToBlock = ['selectstart', 'cut', 'dragstart'];
        const mouseEventsToBlock = ['mousedown', 'mousemove', 'mouseup'];

        logger.debug('超级复制', 'Intercepting events on:', targetNode);

        // 处理非鼠标事件 - 这些可以直接拦截
        nonMouseEventsToBlock.forEach(eventType => {
            targetNode.addEventListener(eventType, event => {
                logger.debug('超级复制', `Intercepted ${eventType}`);
                event.stopPropagation();
            }, true); // Use capture phase
        });

        // 处理鼠标事件 - 需要检查是否是浮动按钮
        mouseEventsToBlock.forEach(eventType => {
            targetNode.addEventListener(eventType, event => {
                // 检查事件目标是否是浮动按钮或其子元素
                const floatingBtn = document.getElementById('smartcopy-floating-btn');
                if (floatingBtn && (event.target === floatingBtn || floatingBtn.contains(event.target))) {
                    // 如果是浮动按钮，不拦截事件
                    logger.debug('超级复制', `检测到浮动按钮的${eventType}事件，不拦截`);
                    return true;
                }

                logger.debug('超级复制', `Intercepted ${eventType}`);
                event.stopPropagation();
            }, true); // Use capture phase
        });

        // Remove attribute-based handlers
        targetNode.querySelectorAll('[onselectstart], [oncopy], [oncut], [oncontextmenu], [ondragstart]').forEach(el => {
            // 检查元素是否是浮动按钮或其子元素
            const floatingBtn = document.getElementById('smartcopy-floating-btn');
            if (floatingBtn && (el === floatingBtn || floatingBtn.contains(el))) {
                // 如果是浮动按钮，不移除属性
                logger.debug('超级复制', `检测到浮动按钮的属性，不移除`);
                return;
            }

            // 移除所有事件属性
            const allEventsToBlock = [...nonMouseEventsToBlock, ...mouseEventsToBlock];
            allEventsToBlock.forEach(ev => { if(el[`on${ev}`]) el[`on${ev}`] = null; }); // More compatible way
            allEventsToBlock.forEach(ev => el.removeAttribute(`on${ev}`)); // Also remove attribute
        });

        logger.debug('超级复制', 'Event listeners intercepted for Super Copy.');
    };

    // Function to remove problematic attributes for Super Copy
    const removeSuperCopyAttributes = (targetNode = document.documentElement) => {
        logger.debug('超级复制', 'Removing attributes on:', targetNode);
        targetNode.querySelectorAll('[unselectable]').forEach(el => el.removeAttribute('unselectable'));
        logger.debug('超级复制', 'Problematic attributes removed for Super Copy.');
    };

    // Main function to apply all bypass techniques for Super Copy
    const applySuperCopyBypass = (node = document.documentElement) => {
        // 如果超级复制未启用，不执行任何操作
        if (!superCopyEnabled) {
            logger.debug('超级复制', `超级复制已禁用，不应用绕过技术`);
            return;
        }

        logger.debug('超级复制', `应用超级复制绕过技术到:`, node);

        // 添加CSS样式
        addSuperCopyStyles();

        // 移除事件监听器
        removeSuperCopyEventListeners(node);

        // 移除限制属性
        removeSuperCopyAttributes(node);

        // 处理全局事件拦截
        handleGlobalEventInterception();

        // 处理特定的复制保护脚本
        handleSpecificProtections();
    };

    // 处理全局事件拦截 - 优化版本
    // 全局事件拦截器的引用
    let globalContextMenuHandler = null;
    let globalCopyHandler = null;
    let globalSelectStartHandler = null;
    let globalClickHandler = null;

    const handleGlobalEventInterception = () => {
        try {
            // 如果超级复制未启用，不执行事件拦截
            if (!superCopyEnabled) {
                console.log(`[超级复制] 超级复制已禁用，不执行全局事件拦截`);
                return;
            }

            console.log(`[超级复制] 执行全局事件拦截`);

            // 安全地覆盖文档事件
            const safelyOverrideEvent = (eventName) => {
                try {
                    if (document[eventName] && typeof document[eventName] === 'function') {
                        const eventString = document[eventName].toString();
                        if (eventString.includes('return false') || eventString.includes('preventDefault')) {
                            console.log(`[超级复制] 移除${eventName}限制`);
                            document[eventName] = null;
                        }
                    }
                } catch (err) {
                    console.error(`[超级复制] 处理${eventName}时出错:`, err);
                }
            };

            // 处理常见的限制事件
            const eventsToOverride = ['oncontextmenu', 'ondragstart', 'onselectstart', 'onkeydown', 'onmousedown', 'oncopy', 'oncut', 'onpaste', 'onclick'];
            eventsToOverride.forEach(safelyOverrideEvent);

            // 移除之前添加的事件处理程序
            if (globalContextMenuHandler) {
                document.removeEventListener('contextmenu', globalContextMenuHandler, true);
            }
            if (globalCopyHandler) {
                document.removeEventListener('copy', globalCopyHandler, true);
            }
            if (globalSelectStartHandler) {
                document.removeEventListener('selectstart', globalSelectStartHandler, true);
            }
            if (globalClickHandler) {
                document.removeEventListener('click', globalClickHandler, true);
            }

            // 添加新的事件处理程序
            globalContextMenuHandler = function(e) {
                if (!superCopyEnabled) return true;

                // 检查事件目标是否是浮动按钮或其子元素
                const floatingBtn = document.getElementById('smartcopy-floating-btn');
                if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
                    // 如果是浮动按钮，不拦截事件
                    console.log(`[超级复制] 检测到浮动按钮的contextmenu事件，不拦截`);
                    return true;
                }

                e.stopImmediatePropagation();
                return true;
            };

            globalCopyHandler = function(e) {
                if (!superCopyEnabled) return true;

                // 检查事件目标是否是浮动按钮或其子元素
                const floatingBtn = document.getElementById('smartcopy-floating-btn');
                if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
                    // 如果是浮动按钮，不拦截事件
                    console.log(`[超级复制] 检测到浮动按钮的copy事件，不拦截`);
                    return true;
                }

                e.stopImmediatePropagation();
                return true;
            };

            globalSelectStartHandler = function(e) {
                if (!superCopyEnabled) return true;

                // 检查事件目标是否是浮动按钮或其子元素
                const floatingBtn = document.getElementById('smartcopy-floating-btn');
                if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
                    // 如果是浮动按钮，不拦截事件
                    console.log(`[超级复制] 检测到浮动按钮的selectstart事件，不拦截`);
                    return true;
                }

                e.stopImmediatePropagation();
                return true;
            };

            // 添加点击事件处理程序，确保浮动按钮的点击事件不被拦截
            globalClickHandler = function(e) {
                if (!superCopyEnabled) return true;

                // 检查事件目标是否是浮动按钮或其子元素
                const floatingBtn = document.getElementById('smartcopy-floating-btn');
                if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
                    // 如果是浮动按钮，不拦截事件
                    console.log(`[超级复制] 检测到浮动按钮的click事件，不拦截`);
                    return true;
                }

                // 对于其他元素，我们不拦截点击事件，只是记录日志
                return true;
            };

            // 注册新的事件处理程序
            document.addEventListener('contextmenu', globalContextMenuHandler, true);
            document.addEventListener('copy', globalCopyHandler, true);
            document.addEventListener('selectstart', globalSelectStartHandler, true);
            document.addEventListener('click', globalClickHandler, true);

            console.log(`[超级复制] 全局事件拦截器已注册`);
        } catch (err) {
            console.error('[超级复制] 处理全局事件时出错:', err);
        }
    };

    // 移除全局事件拦截器
    const removeGlobalEventInterception = () => {
        try {
            console.log(`[超级复制] 移除全局事件拦截器`);

            if (globalContextMenuHandler) {
                document.removeEventListener('contextmenu', globalContextMenuHandler, true);
                globalContextMenuHandler = null;
            }

            if (globalCopyHandler) {
                document.removeEventListener('copy', globalCopyHandler, true);
                globalCopyHandler = null;
            }

            if (globalSelectStartHandler) {
                document.removeEventListener('selectstart', globalSelectStartHandler, true);
                globalSelectStartHandler = null;
            }

            if (globalClickHandler) {
                document.removeEventListener('click', globalClickHandler, true);
                globalClickHandler = null;
            }

            console.log(`[超级复制] 全局事件拦截器已移除`);
        } catch (err) {
            console.error('[超级复制] 移除全局事件拦截器时出错:', err);
        }
    };

    // 处理特定的复制保护脚本 - 优化版本
    const handleSpecificProtections = () => {
        try {
            // 添加启用文本选择的样式
            const styleId = 'enhanced-tab-copy-override-style';

            // 检查样式是否已经存在，避免重复添加
            if (!document.getElementById(styleId)) {
                const style = document.createElement('style');
                style.id = styleId;
                style.innerHTML = `
                    * {
                        -webkit-user-select: auto !important;
                        -moz-user-select: auto !important;
                        -ms-user-select: auto !important;
                        user-select: auto !important;
                    }

                    [unselectable="on"] {
                        -webkit-user-select: auto !important;
                        -moz-user-select: auto !important;
                        -ms-user-select: auto !important;
                        user-select: auto !important;
                    }

                    [onselectstart="return false"] {
                        -webkit-user-select: auto !important;
                        -moz-user-select: auto !important;
                        -ms-user-select: auto !important;
                        user-select: auto !important;
                    }

                    /* 移除其他常见的限制样式 */
                    .disable-select, .noselect, .no-select {
                        -webkit-user-select: auto !important;
                        -moz-user-select: auto !important;
                        -ms-user-select: auto !important;
                        user-select: auto !important;
                    }
                `;
                // 安全地添加样式元素，确保在document.head不存在的情况下也能正常工作
                if (document.head) {
                    document.head.appendChild(style);
                } else if (document.documentElement) {
                    document.documentElement.appendChild(style);
                } else if (document.body) {
                    document.body.appendChild(style);
                } else {
                    console.warn('[SmartCopy] 无法添加样式元素，document.head、document.documentElement和document.body都不存在');
                    return; // 如果无法添加样式，则提前返回
                }
                console.log('[SmartCopy] 添加了文本选择样式覆盖');
            }

            // 安全地覆盖保护函数
            const commonProtectionFunctions = [
                'addMultiEventListener',
                'nocontextmenu',
                'disableCopy',
                'disableSelection',
                'disablePaste',
                'disableRightClick',
                'show_toast'
            ];

            commonProtectionFunctions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        console.log(`[SmartCopy] 移除保护函数: ${funcName}`);
                        window[funcName] = function() { return true; };
                    }
                } catch (err) {
                    console.error(`[SmartCopy] 处理函数${funcName}时出错:`, err);
                }
            });
        } catch (err) {
            console.error('[SmartCopy] 处理特定保护时出错:', err);
        }
    };

    // MutationObserver setup for Super Copy
    const setupSuperCopyObserver = () => {
        // 如果超级复制未启用，不设置观察器
        if (!superCopyEnabled) {
            console.log('[超级复制] 超级复制已禁用，不设置MutationObserver');
            if (superCopyObserver) {
                superCopyObserver.disconnect();
                superCopyObserver = null;
                console.log('[超级复制] 已断开现有MutationObserver连接');
            }
            return;
        }

        console.log('[超级复制] 设置MutationObserver');

        if (superCopyObserver) superCopyObserver.disconnect();

        superCopyObserver = new MutationObserver((mutations) => {
            // 如果超级复制被禁用，停止处理
            if (!superCopyEnabled) {
                console.log('[超级复制] 超级复制已禁用，不处理DOM变化');
                return;
            }

            console.log('[超级复制] 检测到DOM变化');
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(newNode => {
                        if (newNode.nodeType === Node.ELEMENT_NODE) {
                            console.log('[超级复制] 新节点添加，应用绕过:', newNode);
                            applySuperCopyBypass(newNode); // 应用到新节点
                        }
                    });
                } else if (mutation.type === 'attributes') {
                    const relevantAttrs = ['style', 'class', 'unselectable', 'onselectstart', 'oncopy', 'oncut', 'oncontextmenu', 'ondragstart'];
                    if (relevantAttrs.includes(mutation.attributeName)) {
                         console.log(`[超级复制] 属性变更 (${mutation.attributeName})，应用绕过:`, mutation.target);
                        applySuperCopyBypass(mutation.target); // 重新应用到修改的元素
                    }
                }
            });
        });

        superCopyObserver.observe(document.documentElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class', 'unselectable', 'onselectstart', 'oncopy', 'oncut', 'oncontextmenu', 'ondragstart']
        });
        console.log('[超级复制] MutationObserver设置完成');
    };

    // Cleanup function for Super Copy (defined globally)
    window.cleanupSuperCopy = () => {
        try {
            console.log('[SmartCopy] Cleaning up Super Copy...');
            if (superCopyObserver) {
                try {
                    superCopyObserver.disconnect();
                    superCopyObserver = null;
                    console.log('[SmartCopy] Observer disconnected.');
                } catch (e) {
                    console.warn('[SmartCopy] Error disconnecting observer:', e);
                }
            }
            if (superCopyReapplyInterval) {
                try {
                    clearInterval(superCopyReapplyInterval);
                    superCopyReapplyInterval = null;
                    console.log('[SmartCopy] Reapply interval cleared.');
                } catch (e) {
                    console.warn('[SmartCopy] Error clearing interval:', e);
                }
            }
        } catch (e) {
            console.warn('[SmartCopy] Error in cleanupSuperCopy:', e);
        }
        const styleElement = document.getElementById(SUPER_COPY_STYLE_ID);
        if (styleElement) {
            styleElement.remove();
            console.log('[SmartCopy] Super Copy styles removed.');
        }
        const statusElement = document.getElementById(STATUS_DIV_ID);
        if (statusElement) {
            // Optionally hide it instead of removing immediately
            statusElement.style.opacity = '0';
            statusElement.style.transform = 'translateY(20px)';
           // setTimeout(() => statusElement.remove(), 600); // Remove after transition
             console.log('[SmartCopy] Status message element hidden/removed.');
        }

        // Removing dynamically added capture event listeners is hard/unreliable without references.
        // Page refresh is the best way to guarantee removal.

        // Remove unload listener if we added one
        if (window.hasSuperCopyUnloadListener) {
            window.removeEventListener('unload', window.cleanupSuperCopy);
            window.hasSuperCopyUnloadListener = false;
            console.log('[SmartCopy] Unload listener removed.');
        }
        console.log('[SmartCopy] Super Copy cleanup finished.');
    };

    // 函数：停用超级复制功能 (defined globally)
    function disableSuperCopy() {
        console.log('[SmartCopy] Disabling Super Copy...');
        if (!superCopyEnabled && !document.getElementById(SUPER_COPY_STYLE_ID)) {
            console.warn('[SmartCopy] Super Copy seems already disabled. Skipping.');
            return;
        }
        superCopyEnabled = false;
        // 使用 Chrome 存储 API 持久化状态
        chrome.storage.local.set({'superCopyEnabled': false}); // Persist state

        // 无论 cleanupSuperCopy 是否存在，都强制彻底清理
        const styleElement = document.getElementById(SUPER_COPY_STYLE_ID);
        if (styleElement) styleElement.remove();
        if (window.superCopyObserver) {
            window.superCopyObserver.disconnect();
            window.superCopyObserver = null;
        }
        if (window.superCopyReapplyInterval) {
            clearInterval(window.superCopyReapplyInterval);
            window.superCopyReapplyInterval = null;
        }
        if (typeof window.cleanupSuperCopy === 'function') {
            window.cleanupSuperCopy();
        }
        showStatusMessage(chrome.i18n.getMessage('super_copy_disabled_notification'), 'success');
    }

    // 一键清理所有扩展存储（local 和 sync）
function clearAllExtensionStorage() {
    try {
        chrome.storage.local.clear(() => {
            if (chrome.runtime.lastError) {
                console.error('local 存储清理失败:', chrome.runtime.lastError);
            } else {
                console.log('local 存储已清理');
            }
        });
        if (chrome.storage.sync) {
            chrome.storage.sync.clear(() => {
                if (chrome.runtime.lastError) {
                    console.error('sync 存储清理失败:', chrome.runtime.lastError);
                } else {
                    console.log('sync 存储已清理');
                }
            });
        }
        showStatusMessage('storage_cleared', 'success');
    } catch (e) {
        console.error('清理扩展存储出错:', e);
        showStatusMessage('storage_clear_failed', 'error');
    }
}
// 可在控制台 window.clearAllExtensionStorage() 调用
window.clearAllExtensionStorage = clearAllExtensionStorage;

// 应用超级复制功能 - 破解网站复制限制
    function applySuperCopy() {
        // 检查超级复制是否启用
        if (!superCopyEnabled) {
            console.log('[超级复制] 超级复制已禁用，不应用功能');
            // 确保清理所有超级复制相关的事件和样式
            cleanupSuperCopy();
            return;
        }

        // 如果已经启用且样式存在，彻底跳过，无需重复注册和日志
        if (document.getElementById(SUPER_COPY_STYLE_ID)) {
            console.info('[超级复制] 超级复制已经激活，跳过应用');
            return;
        }

        console.log('[超级复制] 开始应用超级复制功能...');

        // 注册所有超级复制相关事件
        registerSuperCopyEvents();

        try {
            // 检查当前网站是否在排除列表
            const currentHost = window.location.hostname;
            const isExcludedSite = excludedSites.some(site => currentHost.includes(site));

            if (isExcludedSite) {
                console.log(`[SmartCopy] Site ${currentHost} is excluded. Super Copy not applied.`);
                showStatusMessage('site_excluded', 'error');
                superCopyEnabled = false;
                // 使用 Chrome 存储 API 持久化状态
                chrome.storage.local.set({'superCopyEnabled': false});
                return; // Stop execution for excluded sites
            }

            console.log('[SmartCopy] Applying robust bypass universally...');

            // Initial application (using the globally defined functions)
            applySuperCopyBypass(document.documentElement);
            setupSuperCopyObserver();

            // Periodic re-application as a fallback
            if (superCopyReapplyInterval) clearInterval(superCopyReapplyInterval); // Clear previous if any
            superCopyReapplyInterval = setInterval(() => {
                if (!superCopyEnabled) { // Check the global state
                    clearInterval(superCopyReapplyInterval);
                    superCopyReapplyInterval = null;
                    return;
                }
                console.log('[SmartCopy] Periodic re-application triggered.');
                // Re-apply styles and potentially attribute removal, but maybe not listeners again?
                addSuperCopyStyles();
                removeSuperCopyAttributes(document.documentElement);
                // Avoid re-adding listeners repeatedly via interval
                // removeSuperCopyEventListeners(document.documentElement);
            }, 7500); // Re-apply slightly less often: every 7.5 seconds

            // 不再显示"超级复制已启用"的通知，避免在每次访问网站时都显示
            // 原代码: showStatusMessage('super_copy_enabled_notification', 'success');
            console.log('[超级复制] 超级复制已启用，但不显示通知');

            // Ensure cleanup happens if the script context is invalidated (e.g., page navigation)
            // Use a flag to avoid adding multiple listeners
            if (!window.hasSuperCopyUnloadListener) {
                window.addEventListener('unload', window.cleanupSuperCopy, { once: true });
                window.hasSuperCopyUnloadListener = true;
                console.log('[SmartCopy] Unload listener added.');
            }

        } catch (error) {
            console.error('超级复制功能初始化失败:', error);
            showStatusMessage('super_copy_init_failed', 'error');
            superCopyEnabled = false; // Ensure state is false on error
            // 使用 Chrome 存储 API 持久化状态
            chrome.storage.local.set({'superCopyEnabled': false});
            // Attempt cleanup even on error
            if (typeof window.cleanupSuperCopy === 'function') {
                window.cleanupSuperCopy();
            }
        }
    }

// --- Initialization and Menu Commands ---

// 自动复制功能初始化绑定（防止首次加载不生效）
function initAutoTextCopyListeners() {
  if (settings.autoTextCopy) {
    document.removeEventListener('mouseup', handleTextSelection);
    document.addEventListener('mouseup', handleTextSelection);
    // selectionchange 自动复制
    if (!window._autoCopySelectionChangeHandler) {
      let lastSelection = '';
      let selectionDebounceTimer = null;
      window._autoCopySelectionChangeHandler = function () {
        // 只依赖 autoTextCopy
        if (!settings.autoTextCopy) return;

        // 如果鼠标按下状态，说明用户正在选择文本，暂不处理
        if (isMouseDown) return;

        const selection = window.getSelection();
        if (!selection) return;
        const selectedText = selection.toString().trim();
        // 排除输入框/可编辑区域
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) return;

        // 检查是否与点击前的选择相同（用于判断是否是点击页面而非新选择）
        const isSameAsBeforeClick = selectedText === window._lastSelectionBeforeClick;

        // 如果是点击页面而非新选择，则不进行复制
        if (isSameAsBeforeClick) {
          console.log('selectionchange (initAutoTextCopyListeners): 检测到点击页面而非新选择，跳过复制操作');
          return;
        }

        // 如果按下了Shift键，则收集多段选择的文本
        if (isShiftKeyPressed && selectedText) {
          // 避免添加重复的文本
          if (!multiSelectionTexts.includes(selectedText)) {
            multiSelectionTexts.push(selectedText);
            console.log(`添加到多段选择: ${selectedText}`);

            // 使用对象格式，让showCopyNotification函数处理国际化
            showCopyNotification({
              messageKey: 'added_to_multi_selection',
              messageParams: [multiSelectionTexts.length.toString()],
              useI18n: true,
              type: 'success'
            });

            // 调试日志
            console.log('多段选择消息已发送，段数:', multiSelectionTexts.length);
            console.log('当前浏览器UI语言:', chrome.i18n.getUILanguage());
            console.log('原始浏览器语言:', navigator.language || navigator.userLanguage);
          }
          return; // 不立即复制，等待Shift键释放
        }

        // 检查选中文本是否达到最小字符数
        if (selectedText && selectedText.length >= (settings.minCharacters || 1)) {
          // 检查是否与最近复制的文本相同
          if (selectedText === lastCopiedText) {
            // 如果是相同文本，检查是否是由于用户点击页面导致的重复复制
            const timeSinceLastCopy = Date.now() - lastCopyTime;
            if (timeSinceLastCopy < 1000) { // 1秒内的重复复制视为重复操作
              console.log('selectionchange: 检测到短时间内重复复制相同文本，跳过复制操作');
              return;
            }
          }

          // 只有当文本与上次选择不同时才更新lastSelection并复制
          if (selectedText !== lastSelection) {
            lastSelection = selectedText;

            // 延迟处理，避免在多段选择过程中多次触发
            if (mouseUpDelayTimer) {
              clearTimeout(mouseUpDelayTimer);
            }

            mouseUpDelayTimer = setTimeout(() => {
              copyTextToClipboard(selectedText);
              mouseUpDelayTimer = null;
            }, 100);
          }
        }
      };
      window._autoCopySelectionChangeDebounced = function() {
        if (selectionDebounceTimer) clearTimeout(selectionDebounceTimer);
        selectionDebounceTimer = setTimeout(window._autoCopySelectionChangeHandler, 200);
      };
    }
    document.removeEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
    document.addEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
    console.log('已启用自动复制功能（含 selectionchange）');
  }
}

// 注意：不再立即执行此函数，而是在loadSettings()完成后执行
// loadSettings().then(() => {
//   console.log('设置加载完成，当前最小字符数:', settings.minCharacters);
//   // 初始化自动复制功能
//   initAutoTextCopyListeners();
// });

// 在 Chrome 扩展中，菜单命令通常通过弹出窗口或上下文菜单实现
// 这里我们只记录日志，实际功能需要在 popup.js 或 background.js 中实现
function registerSuperCopyMenuCommand() {
    console.log('[SmartCopy] 菜单命令状态：' +
                (superCopyEnabled ? '超级复制已启用' : '超级复制未启用'));

    // 添加消息监听器，响应来自弹出窗口或背景脚本的命令
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        console.log('[SmartCopy] 收到消息:', request);

        if (request.action === 'toggleSuperCopy') {
            if (superCopyEnabled) {
                disableSuperCopy();
                sendResponse({success: true, enabled: false});
            } else {
                applySuperCopy();
                sendResponse({success: true, enabled: true});
            }
        } else if (request.action === 'getSuperCopyStatus') {
            sendResponse({enabled: superCopyEnabled});
        }

        return true; // 保持消息通道打开
    });
}

// 处理复制事件
function handleCopy(event) {
    // 如果超级复制未启用，则不拦截复制
    if (!superCopyEnabled) {
        return true;
    }

    // 获取选中的文本
    const selection = window.getSelection();
    const selectedText = selection ? selection.toString() : '';

    if (selectedText) {
        console.log('[SmartCopy] 成功复制文本:', selectedText.substring(0, 50) + (selectedText.length > 50 ? '...' : ''));

        // 如果需要处理复制格式或其他逻辑，可以在这里添加

        // 显示复制成功通知
        showCopyNotification({
          messageKey: 'text_copied',
          useI18n: true,
          type: 'success'
        });
    }
}

// 初始化
function initialize() {
    console.log('[超级复制] 初始化脚本...');

    // 先获取当前网站的超级复制设置
    (async function() {
        try {
            // 获取当前网站的超级复制设置
            // 不再调用已删除的 getSuperCopyEnabled 函数
            // 而是直接从存储中获取设置
            const domain = getDomain();
            const data = await chrome.storage.sync.get(['superCopySiteSettings']);

            if (data.superCopySiteSettings && typeof data.superCopySiteSettings === 'object' && domain in data.superCopySiteSettings) {
                superCopyEnabled = Boolean(data.superCopySiteSettings[domain]);
            } // 否则保持 superCopyEnabled 的当前值

            console.log(`[超级复制] 获取到当前网站设置: ${superCopyEnabled ? '已启用' : '已禁用'}`);

            // 根据超级复制状态应用或清理功能
            if (superCopyEnabled) {
                console.log('[超级复制] 当前网站超级复制已启用，注册相关事件');
                registerSuperCopyEvents();
            } else {
                console.log('[超级复制] 当前网站超级复制已禁用，清理相关事件');
                unregisterSuperCopyEvents();
                // 确保彻底清理
                cleanupSuperCopy();
            }
        } catch (error) {
            console.error('[超级复制] 初始化超级复制状态时出错:', error);
            // 出错时默认不启用
            superCopyEnabled = false;
            unregisterSuperCopyEvents();
            cleanupSuperCopy();
        }
    })();

    // 注册菜单命令
    registerSuperCopyMenuCommand();

    // 添加消息监听器来响应清除历史的命令
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
        if (request.action === 'clearCopyHistory') {
            clearCopyHistory();
            sendResponse({success: true});
        }
    });

    console.log('[超级复制] 初始化完成');
}

// Run initialization when the script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

// --- Cleanup on script unload (Tampermonkey specific) ---
// Note: The 'unload' listener inside applySuperCopy handles dynamic cleanup.
// This could be a place for more static cleanup if needed.

console.log('[SmartCopy] Script loaded and initialized.');

// --- History Management Functions ---
// 保存复制历史到 Chrome 存储
function saveCopyHistory(history) {
    try {
        const data = {};
        data[COPY_HISTORY_KEY] = history;
        chrome.storage.local.set(data);
    } catch (err) {
        console.error('保存复制历史失败:', err);
    }
}

// 加载复制历史
function loadCopyHistory() {
    return new Promise((resolve) => {
        try {
            chrome.storage.local.get([COPY_HISTORY_KEY], function(result) {
                resolve(result[COPY_HISTORY_KEY] || []);
            });
        } catch (err) {
            console.error('加载复制历史失败:', err);
            resolve([]);
        }
    });
}

// 清除复制历史
function clearCopyHistory() {
    try {
        chrome.storage.local.remove([COPY_HISTORY_KEY]);
        console.log('复制历史已清除');
    } catch (err) {
        console.error('清除复制历史失败:', err);
    }
}

// 用于跟踪鼠标按下状态
let isMouseDown = false;
let mouseDownTime = 0;
let mouseUpDelayTimer = null;

// 添加鼠标按下事件监听器
document.addEventListener('mousedown', function(event) {
  // 忽略输入框和可编辑元素中的操作
  if (isEditableElement(event.target)) {
    return;
  }

  isMouseDown = true;
  mouseDownTime = Date.now();

  // 清除之前的mouseup延迟计时器
  if (mouseUpDelayTimer) {
    clearTimeout(mouseUpDelayTimer);
    mouseUpDelayTimer = null;
  }

  // 记录点击时的选择状态，用于在mouseup时判断是否是新的选择
  window._lastSelectionBeforeClick = window.getSelection().toString().trim();
  console.log('mousedown: 记录点击前的选择文本:', window._lastSelectionBeforeClick);
});

// 用于存储多段选择的文本
let multiSelectionTexts = [];
let isShiftKeyPressed = false;

// 监听shift键按下和释放
document.addEventListener('keydown', function(event) {
  if (event.key === 'Shift') {
    isShiftKeyPressed = true;
  }
});

document.addEventListener('keyup', function(event) {
  if (event.key === 'Shift') {
    isShiftKeyPressed = false;

    // 如果有多段选择的文本，则在shift键释放时处理
    if (multiSelectionTexts.length > 0) {
      const combinedText = multiSelectionTexts.join('\n\n');
      if (combinedText.length >= settings.minCharacters) {
        copyTextToClipboard(combinedText);
      }
      multiSelectionTexts = []; // 清空多段选择
    }
  }
});

// 处理文本选择
function handleTextSelection(event) {
  // 忽略输入框和可编辑元素中的选择
  if (isEditableElement(event.target)) {
    return;
  }

  // 标记鼠标已抬起
  isMouseDown = false;

  // 获取选中的文本
  const selectedText = window.getSelection().toString().trim();

  // 添加调试日志
  console.log(`选中文本长度: ${selectedText.length}, 最小字符数设置: ${settings.minCharacters}, Shift键状态: ${isShiftKeyPressed}`);

  // 检查是否与点击前的选择相同（用于判断是否是点击页面而非新选择）
  const isSameAsBeforeClick = selectedText === window._lastSelectionBeforeClick;

  // 如果是点击页面而非新选择，则不进行复制
  if (isSameAsBeforeClick) {
    console.log('检测到点击页面而非新选择，跳过复制操作');
    return;
  }

  // 如果按下了Shift键，则收集多段选择的文本
  if (isShiftKeyPressed && selectedText) {
    // 避免添加重复的文本
    if (!multiSelectionTexts.includes(selectedText)) {
      multiSelectionTexts.push(selectedText);
      console.log(`添加到多段选择: ${selectedText}`);

      // 使用对象格式，让showCopyNotification函数处理国际化
      showCopyNotification({
        messageKey: 'added_to_multi_selection',
        messageParams: [multiSelectionTexts.length.toString()],
        useI18n: true,
        type: 'success'
      });

      // 调试日志
      console.log('多段选择消息已发送，段数:', multiSelectionTexts.length);
      console.log('当前浏览器UI语言:', chrome.i18n.getUILanguage());
      console.log('原始浏览器语言:', navigator.language || navigator.userLanguage);
    }
    return; // 不立即复制，等待Shift键释放
  }

  // 计算从mousedown到mouseup的时间差，判断是否是快速点击
  const mouseUpTime = Date.now();
  const isQuickClick = (mouseUpTime - mouseDownTime) < 200; // 200毫秒内的点击视为快速点击

  // 如果是快速点击且没有选中新文本，则不进行复制
  if (isQuickClick && (!selectedText || selectedText.length < settings.minCharacters)) {
    console.log('检测到快速点击，不进行复制');
    return;
  }

  // 延迟处理选择，以便在用户完成多段选择后只处理一次
  if (mouseUpDelayTimer) {
    clearTimeout(mouseUpDelayTimer);
  }

  mouseUpDelayTimer = setTimeout(() => {
    // 检查选中文本是否达到最小字符数
    if (selectedText && selectedText.length >= settings.minCharacters) {
      // 检查是否与最近复制的文本相同
      if (selectedText === lastCopiedText) {
        // 如果是相同文本，检查是否是由于用户点击页面导致的重复复制
        const timeSinceLastCopy = Date.now() - lastCopyTime;
        if (timeSinceLastCopy < 1000) { // 1秒内的重复复制视为重复操作
          console.log('检测到短时间内重复复制相同文本，跳过复制操作');
          return;
        }
      }

      copyTextToClipboard(selectedText);
    }

    mouseUpDelayTimer = null;
  }, 100); // 100毫秒延迟，给用户足够的时间完成多段选择
}

// 检查元素是否可编辑
function isEditableElement(element) {
  const tagName = element.tagName.toLowerCase();
  const contentEditable = element.getAttribute('contenteditable');

  return (
    tagName === 'input' ||
    tagName === 'textarea' ||
    contentEditable === 'true' ||
    contentEditable === ''
  );
}

// 用于跟踪最后复制的文本，避免重复添加到历史记录
let lastCopiedText = '';
let lastCopyTime = 0;
const COPY_DEBOUNCE_TIME = 1000; // 1000毫秒内的复制操作视为同一次操作
let pendingCopyRequest = null; // 用于跟踪正在进行的复制请求

// 获取选中文本的HTML内容
function getSelectionHtml() {
  let html = '';
  const selection = window.getSelection();

  if (!selection || selection.rangeCount === 0) {
    console.warn('没有选中内容或选区数量为0');
    return html;
  }

  try {
    // 创建一个临时容器
    const container = document.createElement('div');

    // 处理多个选区（如使用Shift+点击选择的多段文本）
    for (let i = 0; i < selection.rangeCount; i++) {
      const range = selection.getRangeAt(i);

      // 跳过空选区
      if (range.collapsed) {
        continue;
      }

      // 克隆选区内容并添加到容器
      const fragment = range.cloneContents();

      // 如果不是第一个选区，添加分隔符
      if (i > 0 && container.childNodes.length > 0) {
        const separator = document.createElement('p');
        separator.innerHTML = '<br>';
        container.appendChild(separator);
      }

      container.appendChild(fragment);
    }

    // 获取HTML内容
    html = container.innerHTML;

    // 如果HTML为空但有文本选择，创建一个基本的段落
    if (!html && selection.toString().trim()) {
      html = '<p>' + selection.toString().trim() + '</p>';
    }

    console.log('获取到的HTML内容:', html);
  } catch (error) {
    console.error('获取选中内容HTML时出错:', error);
  }

  return html;
}

// 将HTML转换为Markdown
function convertHtmlToMarkdown(html) {
  try {
    // 如果HTML为空，直接返回空字符串
    if (!html || html.trim() === '') {
      console.warn('HTML内容为空，无法转换为Markdown');
      return '';
    }

    // 使用TurndownService将HTML转换为Markdown
    if (typeof TurndownService !== 'undefined') {
      console.log('使用TurndownService转换HTML到Markdown');

      try {
        // 创建TurndownService实例
        const turndownService = new TurndownService({
          headingStyle: 'atx',
          codeBlockStyle: 'fenced',
          emDelimiter: '*',
          strongDelimiter: '**',
          bulletListMarker: '-',
          hr: '---',
          br: '  \n'
        });

        // 添加GFM插件支持（如果存在）
        if (typeof turndownPluginGfm !== 'undefined' && typeof turndownPluginGfm.gfm === 'function') {
          turndownService.use(turndownPluginGfm.gfm);
          console.log('已加载GFM插件');
        }

        // 添加增强版表格插件支持（如果存在）
        if (typeof turndownPluginEnhancedTables !== 'undefined' && typeof turndownPluginEnhancedTables.enhancedTables === 'function') {
          turndownService.use(turndownPluginEnhancedTables.enhancedTables);
          console.log('已加载增强版表格插件');
        }

        // 确保HTML有正确的结构
        let processedHtml = html;
        if (!html.includes('<')) {
          // 如果HTML不包含任何标签，将其包装在段落标签中
          processedHtml = '<p>' + html + '</p>';
        }

        // 转换为Markdown
        const markdown = turndownService.turndown(processedHtml);
        console.log('HTML转换为Markdown成功:', markdown);
        return markdown;
      } catch (turndownError) {
        console.error('TurndownService转换失败:', turndownError);
        // 如果TurndownService失败，回退到自定义方法
      }
    }

    // 如果TurndownService不可用或失败，使用自定义方法
    console.log('使用自定义方法转换HTML到Markdown');

    try {
      // 创建一个临时div来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // 处理标题
      const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        const level = parseInt(heading.tagName.substring(1));
        const text = heading.textContent;
        const markdown = '#'.repeat(level) + ' ' + text;
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        heading.parentNode.replaceChild(span, heading);
      });

      // 处理链接
      const links = tempDiv.querySelectorAll('a');
      links.forEach(link => {
        const text = link.textContent;
        const href = link.getAttribute('href');
        const markdown = `[${text}](${href})`;
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        link.parentNode.replaceChild(span, link);
      });

      // 处理加粗文本
      const bolds = tempDiv.querySelectorAll('b, strong');
      bolds.forEach(bold => {
        const text = bold.textContent;
        const markdown = `**${text}**`;
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        bold.parentNode.replaceChild(span, bold);
      });

      // 处理斜体文本
      const italics = tempDiv.querySelectorAll('i, em');
      italics.forEach(italic => {
        const text = italic.textContent;
        const markdown = `*${text}*`;
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        italic.parentNode.replaceChild(span, italic);
      });

      // 处理带有行号的代码块（figure class="highlight bash"）
      const highlightFigures = tempDiv.querySelectorAll('figure.highlight');
      highlightFigures.forEach(figure => {
        // 提取语言信息
        let language = '';
        if (figure.classList) {
          // 检查类名中是否包含语言信息
          Array.from(figure.classList).forEach(function(className) {
            if (className !== 'highlight') {
              language = className;
            }
          });
        }

        // 查找表格元素
        const table = figure.querySelector('table');
        if (!table) return;

        // 查找代码单元格（通常是第二列）
        const codeCell = table.querySelector('.code');
        if (!codeCell) return;

        // 获取代码内容
        let text = '';

        // 替换HTML标签和实体
        const tempHtml = codeCell.innerHTML
          .replace(/<br\s*\/?>/gi, '\n') // 替换<br>标签为换行符
          .replace(/<span[^>]*>(.*?)<\/span>/gi, '$1') // 移除span标签但保留内容
          .replace(/&nbsp;/g, ' '); // 替换HTML空格为普通空格

        // 使用DOM解析器获取纯文本
        const tempElement = document.createElement('div');
        tempElement.innerHTML = tempHtml;
        text = tempElement.textContent;

        // 处理每一行，移除多余空格但保留前导空格（对命令行很重要）
        const lines = text.split('\n');
        const cleanedLines = lines.filter(line => line.trim().length > 0);
        text = cleanedLines.join('\n');

        // 如果没有指定语言但内容看起来像命令行，添加bash语言标记
        if (!language && (text.includes('$') || text.includes('sudo') || text.includes('npm') || text.includes('git'))) {
          language = 'bash';
        }

        // 创建Markdown代码块
        const markdown = '```' + language + '\n' + text + '\n```';

        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        figure.parentNode.replaceChild(span, figure);
      });

      // 处理代码块
      const codes = tempDiv.querySelectorAll('code, pre');
      codes.forEach(code => {
        let text = code.textContent;

        // 检查是否为HTML格式的代码块（包含<br>标签或特定样式）
        const isHtmlCodeBlock = code.innerHTML.includes('<br>') ||
                               (code.parentNode && code.parentNode.tagName.toLowerCase() === 'pre') ||
                               code.getAttribute('style')?.includes('white-space: nowrap');

        // 检查是否为命令行代码块（包含$、sudo等关键词）
        const isCommandBlock =
          code.textContent.includes('$') ||
          code.textContent.includes('sudo') ||
          code.textContent.includes('npm') ||
          code.textContent.includes('git');

        // 检查是否为带有特定样式的代码块
        const hasSpecialStyle =
          code.getAttribute('style') && (
            code.getAttribute('style').includes('background') ||
            code.getAttribute('style').includes('box-shadow') ||
            code.getAttribute('style').includes('border-radius')
          );

        // 处理HTML格式的代码块，确保换行符被正确保留
        if (isHtmlCodeBlock || isCommandBlock || hasSpecialStyle) {
          // 替换HTML的<br>标签为实际的换行符
          text = code.innerHTML
            .replace(/<br\s*\/?>/gi, '\n') // 替换<br>标签为换行符
            .replace(/<span[^>]*>(.*?)<\/span>/gi, '$1') // 移除span标签但保留内容
            .replace(/&nbsp;/g, ' '); // 替换HTML空格为普通空格

          // 使用DOM解析器获取纯文本
          const tempElement = document.createElement('div');
          tempElement.innerHTML = text;
          text = tempElement.textContent;

          // 确保代码块的每一行都被正确处理
          const lines = text.split('\n');

          // 对于命令行代码块，保留每行的前导空格
          if (isCommandBlock) {
            // 移除空行，但保留每行的前导空格
            const cleanedLines = lines.filter(line => line.trim().length > 0);
            text = cleanedLines.join('\n');
          } else {
            // 对于其他代码块，修剪每行
            const cleanedLines = lines.map(line => line.trim()).filter(line => line.length > 0);
            text = cleanedLines.join('\n');
          }
        }

        // 确定代码块的语言
        let language = '';
        if (isCommandBlock) {
          language = 'bash';
        }

        const markdown = (code.tagName.toLowerCase() === 'pre' || isHtmlCodeBlock || isCommandBlock || hasSpecialStyle) ?
          '```' + language + '\n' + text + '\n```' :
          '`' + text + '`';

        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;

        // 如果是pre标签内的code，替换pre标签
        if (code.parentNode && code.parentNode.tagName.toLowerCase() === 'pre') {
          code.parentNode.parentNode.replaceChild(span, code.parentNode);
        } else {
          code.parentNode.replaceChild(span, code);
        }
      });

      // 处理无序列表
      const uls = tempDiv.querySelectorAll('ul');
      uls.forEach(ul => {
        const items = ul.querySelectorAll('li');
        let markdown = '\n';
        items.forEach(item => {
          // 获取列表项文本
          let itemText = item.textContent;

          // 检查文本是否以 • 或 · 或 ● 等符号开头，如果是则移除
          itemText = itemText.replace(/^\s*[•·●○◆◇■□▪▫]+\s*/, '');

          // 添加Markdown格式的列表项
          markdown += '- ' + itemText + '\n';
        });
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        ul.parentNode.replaceChild(span, ul);
      });

      // 处理有序列表
      const ols = tempDiv.querySelectorAll('ol');
      ols.forEach(ol => {
        const items = ol.querySelectorAll('li');
        let markdown = '\n';
        items.forEach((item, index) => {
          markdown += (index + 1) + '. ' + item.textContent + '\n';
        });
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        ol.parentNode.replaceChild(span, ol);
      });

      // 处理图片
      const images = tempDiv.querySelectorAll('img');
      images.forEach(img => {
        const alt = img.getAttribute('alt') || '';
        const src = img.getAttribute('src') || '';
        const markdown = `![${alt}](${src})`;
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        img.parentNode.replaceChild(span, img);
      });

      // 处理引用
      const blockquotes = tempDiv.querySelectorAll('blockquote');
      blockquotes.forEach(quote => {
        const text = quote.textContent;
        const lines = text.split('\n');
        let markdown = '\n';
        lines.forEach(line => {
          if (line.trim()) {
            markdown += '> ' + line.trim() + '\n';
          }
        });
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        quote.parentNode.replaceChild(span, quote);
      });

      // 处理水平线
      const hrs = tempDiv.querySelectorAll('hr');
      hrs.forEach(hr => {
        const markdown = '\n---\n';
        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        hr.parentNode.replaceChild(span, hr);
      });

      // 处理表格
      const tables = tempDiv.querySelectorAll('table');
      tables.forEach(table => {
        // 检查是否为Twitter风格表格（通过URL或内容特征判断）
        const isTwitterStyleTable = isTwitterTable(table) || window.location.hostname.includes('twitter.com');

        // 根据表格类型选择不同的处理方式
        let markdown = '';

        if (isTwitterStyleTable) {
          // 使用专门的Twitter表格处理函数
          markdown = processTwitterStyleTable(table);
        } else {
          // 处理嵌套在section中的内容
          processNestedElements(table);

          // 获取表格的所有行，包括嵌套在section中的行
          const allRows = getAllTableRows(table);

          // 确定表头行
          let headerRow = null;
          let headerCells = [];

          // 首先查找thead中的行
          const theadRows = table.querySelectorAll('thead > tr');
          if (theadRows.length > 0) {
            headerRow = theadRows[0];
            headerCells = getAllCellsInRow(headerRow);
          } else {
            // 如果没有thead，使用第一行作为表头
            if (allRows.length > 0) {
              headerRow = allRows[0];
              headerCells = getAllCellsInRow(headerRow);
            }
          }

          // 处理表头
          if (headerCells.length > 0) {
            markdown += '| ';
            headerCells.forEach(header => {
              markdown += getCellContent(header) + ' | ';
            });
            markdown += '\n| ';

            // 添加分隔行
            headerCells.forEach(() => {
              markdown += '--- | ';
            });
            markdown += '\n';
          } else {
            // 如果没有找到表头，创建一个默认表头
            // 首先确定列数
            let maxCols = 0;
            allRows.forEach(row => {
              const cellCount = getAllCellsInRow(row).length;
              if (cellCount > maxCols) maxCols = cellCount;
            });

            if (maxCols > 0) {
              markdown += '| ';
              for (let i = 0; i < maxCols; i++) {
                markdown += `列 ${i + 1} | `;
              }
              markdown += '\n| ';

              for (let i = 0; i < maxCols; i++) {
                markdown += '--- | ';
              }
              markdown += '\n';
            }
          }

          // 处理表格内容行
          allRows.forEach(row => {
            // 跳过表头行
            if (row === headerRow) return;

            const cells = getAllCellsInRow(row);
            if (cells.length > 0) {
              markdown += '| ';
              cells.forEach(cell => {
                // 获取单元格内容并确保没有多余的换行符
                let cellContent = getCellContent(cell);
                // 替换单元格内容中的换行符为空格
                cellContent = cellContent.replace(/\n/g, ' ');
                markdown += cellContent + ' | ';
              });
              markdown += '\n';
            }
          });
        }

        const span = document.createElement('span');
        span.setAttribute('data-markdown', markdown);
        span.textContent = markdown;
        table.parentNode.replaceChild(span, table);
      });

      // 判断是否为Twitter风格表格
      function isTwitterTable(table) {
        // 检查表格类名或属性
        if (table.classList && (
          table.classList.contains('twitter-table') ||
          table.classList.contains('social-media-table') ||
          table.hasAttribute('data-twitter-table')
        )) {
          return true;
        }

        // 检查表格内容特征（如k8s-bench等关键词）
        const tableText = table.textContent.toLowerCase();
        if (tableText.includes('k8s-bench') ||
            tableText.includes('kubectl-ai') ||
            tableText.includes('gemini') ||
            tableText.includes('模型') && tableText.includes('成功') && tableText.includes('失败')) {
          return true;
        }

        return false;
      }

      // 专门处理Twitter风格表格
      function processTwitterStyleTable(table) {
        // 获取所有行
        const rows = Array.from(table.querySelectorAll('tr'));
        if (!rows.length) return '';

        // 获取列数（使用第一行）
        const firstRow = rows[0];
        const columnCount = firstRow.querySelectorAll('th, td').length;

        // 创建一个紧凑的表格，没有多余换行符
        let markdown = '';

        // 处理表头（第一行）
        if (rows.length > 0) {
          const headerCells = Array.from(rows[0].querySelectorAll('th, td'));
          markdown += '|';
          headerCells.forEach(cell => {
            let content = cell.textContent.trim().replace(/\n/g, ' ');
            markdown += ` ${content} |`;
          });

          // 添加分隔行，确保在分隔行后添加换行符
          markdown += '\n|';
          for (let i = 0; i < headerCells.length; i++) {
            markdown += ' --- |';
          }

          // 在分隔行后添加换行符，确保表头和内容之间有换行
          markdown += '\n';
        }

        // 处理数据行，从第二行开始
        for (let i = 1; i < rows.length; i++) {
          const cells = Array.from(rows[i].querySelectorAll('th, td'));
          // 添加行开始标记
          markdown += '|';
          cells.forEach(cell => {
            let content = cell.textContent.trim().replace(/\n/g, ' ');
            markdown += ` ${content} |`;
          });

          // 除了最后一行，每行后都添加换行符
          if (i < rows.length - 1) {
            markdown += '\n';
          }
        }

        return markdown;
      }

      // 处理嵌套元素的辅助函数
      function processNestedElements(node) {
        // 处理嵌套在单元格中的section元素
        // 我们不移除section，而是确保能够正确获取其中的内容
      }

      // 获取表格中的所有行，包括嵌套在section中的行
      function getAllTableRows(table) {
        // 直接子行
        const directRows = Array.from(table.querySelectorAll('tr'));

        // 嵌套在section中的行
        const nestedRows = Array.from(table.querySelectorAll('section tr'));

        // 嵌套在thead/tbody/tfoot中的行
        const sectionRows = Array.from(table.querySelectorAll('thead tr, tbody tr, tfoot tr'));

        // 合并所有行并去重
        const allRows = [...new Set([...directRows, ...nestedRows, ...sectionRows])];

        return allRows;
      }

      // 获取行中的所有单元格，包括嵌套在section中的单元格
      function getAllCellsInRow(row) {
        // 直接子单元格
        const directCells = Array.from(row.querySelectorAll(':scope > th, :scope > td'));

        // 嵌套在section中的单元格
        const nestedCells = Array.from(row.querySelectorAll(':scope > section > th, :scope > section > td, :scope section > th, :scope section > td'));

        // 合并所有单元格并去重
        const allCells = [...new Set([...directCells, ...nestedCells])];

        return allCells;
      }

      // 获取单元格的内容，处理嵌套元素
      function getCellContent(cell) {
        // 如果单元格中有嵌套的复杂内容，我们只获取文本内容
        return cell.textContent.trim();
      }

      // 获取处理后的文本
      const result = tempDiv.textContent;
      console.log('自定义方法转换HTML到Markdown成功:', result);
      return result;
    } catch (customError) {
      console.error('自定义方法转换失败:', customError);
      // 如果自定义方法也失败了，返回原始HTML
      return html;
    }
  } catch (error) {
    console.error('转换HTML到Markdown失败:', error);
    return html;
  }
}

// 复制文本到剪贴板
function copyTextToClipboard(text) {
  // 取消之前的复制请求（如果有）
  if (pendingCopyRequest) {
    clearTimeout(pendingCopyRequest);
    pendingCopyRequest = null;
  }

  // 检查是否与最近复制的文本相同或者是在短时间内的复制操作
  const now = Date.now();
  const isSameText = text === lastCopiedText;
  const isWithinDebounceTime = (now - lastCopyTime) < COPY_DEBOUNCE_TIME;

  // 更新最后复制的文本和时间
  lastCopiedText = text;
  lastCopyTime = now;

  // 根据设置决定复制为纯文本还是Markdown
  let finalText = text;
  let formatKey = 'plain_text'; // 使用消息键而不是本地化后的文本
  console.log('Initial format key:', formatKey);

  // 测试获取格式名称的翻译
  const testFormatName = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
  console.log('Test format name translation:', testFormatName, 'for key:', formatKey);

  // 根据设置决定是否转换为Markdown
  if (settings.copyTextFormat === 'markdown') {
    // 获取选中文本的HTML内容
    const html = getSelectionHtml();
    console.log('选中文本的HTML内容:', html);

    if (html) {
      try {
        // 将HTML转换为Markdown
        finalText = convertHtmlToMarkdown(html);
        console.log('转换后的Markdown:', finalText);

        // 如果转换结果为空或与原文本相同，可能是转换失败，使用原文本
        if (!finalText || finalText.trim() === text.trim()) {
          console.warn('Markdown转换可能失败，使用原文本');
          finalText = text;
        }

        formatKey = 'markdown'; // 使用消息键而不是本地化后的文本
        console.log('Updated format key to markdown');
      } catch (error) {
        console.error('Markdown转换出错:', error);
        finalText = text; // 出错时使用原文本
      }
    } else {
      console.warn('未获取到HTML内容，使用纯文本');
    }
  } else {
    console.log('使用纯文本格式');
  }

  // 如果是相同文本或在防抖时间内，只更新剪贴板但不添加到历史记录
  if (isSameText && isWithinDebounceTime) {
    console.log('检测到短时间内重复选择，只更新剪贴板但不添加到历史记录');
    // 仍然复制到剪贴板，但不添加到历史记录
    navigator.clipboard.writeText(finalText)
      .then(() => {
        // 获取当前浏览器UI语言
        const browserUILanguage = chrome.i18n.getUILanguage();
        console.log('Current browser UI language in copyTextToClipboard (same text):', browserUILanguage);

        // 获取当前语言设置
        chrome.storage.sync.get(['language'], function(result) {
          let currentLanguage = result.language || 'auto';
          let displayLanguage = currentLanguage;

          // 如果是自动设置，则使用浏览器语言
          if (currentLanguage === 'auto') {
            // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
            const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
            console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

            // 优先使用原始浏览器语言
            if (rawNavigatorLanguage.startsWith('zh')) {
              if (rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
                displayLanguage = 'zh_TW';
              } else {
                displayLanguage = 'zh_CN';
              }
            } else if (browserUILanguage.startsWith('zh')) {
              // 如果原始浏览器语言不是中文，但Chrome API返回的是中文，再检查Chrome API的结果
              if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO')) {
                displayLanguage = 'zh_TW';
              } else {
                displayLanguage = 'zh_CN';
              }
            } else {
              // 如果不是中文，强制使用英文
              displayLanguage = 'en';
            }

            // 如果浏览器语言不是中文，但显示语言被设置为中文，强制使用英文
            if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
              console.log('检测到不一致，强制使用英语。当前设置:', displayLanguage, '浏览器语言:', rawNavigatorLanguage);
              displayLanguage = 'en';
            }

            console.log('最终使用的语言 (same text):', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
          }

          console.log('Display language in copyTextToClipboard (same text):', displayLanguage);

          // 获取格式的翻译
          const translatedFormat = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
          console.log('Translated format in copyTextToClipboard (same text):', translatedFormat, 'for key:', formatKey);

          // 根据当前语言构建消息
          let notificationMessage = '';

          // 首先尝试使用 contentGetMessage 获取格式名称的翻译
          let formatName = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
          console.log('Format name from i18n (same text):', formatName, 'for key:', formatKey, 'language:', displayLanguage);

          // 检查翻译是否符合预期语言
          let isCorrectLanguage = true;
          if (displayLanguage.startsWith('zh')) {
            // 对于中文，检查翻译是否包含中文字符
            if (!/[\u4e00-\u9fa5]/.test(formatName)) {
              isCorrectLanguage = false;
              console.log('Translation does not contain Chinese characters, falling back to hardcoded values (same text)');
            }
          } else {
            // 对于英文，检查翻译是否不包含中文字符
            if (/[\u4e00-\u9fa5]/.test(formatName)) {
              isCorrectLanguage = false;
              console.log('Translation contains Chinese characters for non-Chinese language, falling back to hardcoded values (same text)');
            }
          }

          // 如果翻译不符合预期语言，使用硬编码的翻译
          if (!isCorrectLanguage) {
            if (displayLanguage.startsWith('zh')) {
              if (displayLanguage === 'zh_TW') {
                formatName = formatKey === 'markdown' ? 'Markdown' : '純文本';
              } else {
                formatName = formatKey === 'markdown' ? 'Markdown' : '纯文本';
              }
            } else {
              formatName = formatKey === 'markdown' ? 'Markdown' : 'Plain Text';
            }
            console.log('Using hardcoded format name (same text):', formatName);
          }

          // 构建通知消息
          if (displayLanguage.startsWith('zh')) {
            if (displayLanguage === 'zh_TW') {
              notificationMessage = `已複製 ${finalText.length} 個字符 (${formatName})`;
            } else {
              notificationMessage = `已复制 ${finalText.length} 个字符 (${formatName})`;
            }
          } else {
            notificationMessage = `Copied ${finalText.length} characters (${formatName})`;
          }

          console.log('Final format name (same text):', formatName, 'for language:', displayLanguage);

          console.log('Directly constructed notification message (same text):', notificationMessage);

          // 显示通知
          showCopyNotification({
            text: notificationMessage,
            useI18n: false,
            type: 'success'
          });
        });
      })
      .catch(err => {
        console.error('复制失败:', err);
        showCopyNotification(window.contentGetMessage ? window.contentGetMessage('copy_failed_with_error', [err.message]) : chrome.i18n.getMessage('copy_failed_with_error', err.message));
      });
    return;
  }

  // 延迟执行复制操作，以便在用户完成多段选择后只执行一次
  pendingCopyRequest = setTimeout(() => {
    // 确保换行符被正确保留
    navigator.clipboard.writeText(finalText)
      .then(() => {
        // 获取当前浏览器UI语言
        const browserUILanguage = chrome.i18n.getUILanguage();
        console.log('Current browser UI language in copyTextToClipboard:', browserUILanguage);

        // 获取当前语言设置
        chrome.storage.sync.get(['language'], function(result) {
          let currentLanguage = result.language || 'auto';
          let displayLanguage = currentLanguage;

          // 如果是自动设置，则使用浏览器语言
          if (currentLanguage === 'auto') {
            // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
            const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
            console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

            // 优先使用原始浏览器语言
            if (rawNavigatorLanguage.startsWith('zh')) {
              if (rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
                displayLanguage = 'zh_TW';
              } else {
                displayLanguage = 'zh_CN';
              }
            } else if (browserUILanguage.startsWith('zh')) {
              // 如果原始浏览器语言不是中文，但Chrome API返回的是中文，再检查Chrome API的结果
              if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO')) {
                displayLanguage = 'zh_TW';
              } else {
                displayLanguage = 'zh_CN';
              }
            } else {
              // 如果不是中文，强制使用英文
              displayLanguage = 'en';
            }

            // 如果浏览器语言不是中文，但显示语言被设置为中文，强制使用英文
            if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
              console.log('检测到不一致，强制使用英语。当前设置:', displayLanguage, '浏览器语言:', rawNavigatorLanguage);
              displayLanguage = 'en';
            }

            console.log('最终使用的语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
          }

          console.log('Display language in copyTextToClipboard:', displayLanguage);

          // 获取格式的翻译
          const translatedFormat = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
          console.log('Translated format in copyTextToClipboard:', translatedFormat, 'for key:', formatKey);

          // 根据当前语言构建消息
          let notificationMessage = '';

          // 首先尝试使用 contentGetMessage 获取格式名称的翻译
          let formatName = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
          console.log('Format name from i18n:', formatName, 'for key:', formatKey, 'language:', displayLanguage);

          // 检查翻译是否符合预期语言
          let isCorrectLanguage = true;
          if (displayLanguage.startsWith('zh')) {
            // 对于中文，检查翻译是否包含中文字符
            if (!/[\u4e00-\u9fa5]/.test(formatName)) {
              isCorrectLanguage = false;
              console.log('Translation does not contain Chinese characters, falling back to hardcoded values');
            }
          } else {
            // 对于英文，检查翻译是否不包含中文字符
            if (/[\u4e00-\u9fa5]/.test(formatName)) {
              isCorrectLanguage = false;
              console.log('Translation contains Chinese characters for non-Chinese language, falling back to hardcoded values');
            }
          }

          // 如果翻译不符合预期语言，使用硬编码的翻译
          if (!isCorrectLanguage) {
            if (displayLanguage.startsWith('zh')) {
              if (displayLanguage === 'zh_TW') {
                formatName = formatKey === 'markdown' ? 'Markdown' : '純文本';
              } else {
                formatName = formatKey === 'markdown' ? 'Markdown' : '纯文本';
              }
            } else {
              formatName = formatKey === 'markdown' ? 'Markdown' : 'Plain Text';
            }
            console.log('Using hardcoded format name:', formatName);
          }

          // 构建通知消息
          if (displayLanguage.startsWith('zh')) {
            if (displayLanguage === 'zh_TW') {
              notificationMessage = `已複製 ${finalText.length} 個字符 (${formatName})`;
            } else {
              notificationMessage = `已复制 ${finalText.length} 个字符 (${formatName})`;
            }
          } else {
            notificationMessage = `Copied ${finalText.length} characters (${formatName})`;
          }

          console.log('Final format name:', formatName, 'for language:', displayLanguage);

          console.log('Directly constructed notification message:', notificationMessage);

          // 显示通知
          showCopyNotification({
            text: notificationMessage,
            useI18n: false,
            type: 'success'
          });
        });

        // 将复制的文本添加到历史记录
        try {
          // 获取格式名称的翻译
          const formatName = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);

          // 获取当前浏览器UI语言
          const browserUILanguage = chrome.i18n.getUILanguage();

          // 获取当前语言设置
          chrome.storage.sync.get(['language'], function(result) {
            let currentLanguage = result.language || 'auto';
            let displayLanguage = currentLanguage;

            // 如果是自动设置，则使用浏览器语言
            if (currentLanguage === 'auto') {
              // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
              const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
              console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

              // 优先使用原始浏览器语言
              if (rawNavigatorLanguage.startsWith('zh')) {
                if (rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
                  displayLanguage = 'zh_TW';
                } else {
                  displayLanguage = 'zh_CN';
                }
              } else if (browserUILanguage.startsWith('zh')) {
                // 如果原始浏览器语言不是中文，但Chrome API返回的是中文，再检查Chrome API的结果
                if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO')) {
                  displayLanguage = 'zh_TW';
                } else {
                  displayLanguage = 'zh_CN';
                }
              } else {
                // 如果不是中文，强制使用英文
                displayLanguage = 'en';
              }

              // 如果浏览器语言不是中文，但显示语言被设置为中文，强制使用英文
              if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
                console.log('检测到不一致，强制使用英语。当前设置:', displayLanguage, '浏览器语言:', rawNavigatorLanguage);
                displayLanguage = 'en';
              }

              console.log('最终使用的语言 (history):', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
            }

            // 添加到剪贴板历史记录
            // 确保格式名称是正确的
            const formatText = window.contentGetMessage ? window.contentGetMessage(formatKey) : chrome.i18n.getMessage(formatKey);
            const sourceText = window.contentGetMessage ? window.contentGetMessage('auto_copy_source', [formatText]) : chrome.i18n.getMessage('auto_copy_source', [formatText]);
            console.log('Sending to background with source:', sourceText, 'formatKey:', formatKey, 'formatText:', formatText);

            chrome.runtime.sendMessage({
              action: 'addToClipboardHistory',
              text: finalText,
              source: sourceText
            }, response => {
              // 检查是否发生错误
              if (chrome.runtime.lastError) {
                console.error('发送消息失败:', chrome.runtime.lastError);
                // 不需要在这里显示错误通知，因为文本已经复制成功
              }
            });
          });
        } catch (err) {
          console.error('发送消息失败:', err);
          // 仍然显示复制成功通知，因为文本确实已复制
        }
      })
      .catch(err => {
        console.error('复制失败:', err);
        showCopyNotification(window.contentGetMessage ? window.contentGetMessage('copy_failed_with_error', [err.message]) : chrome.i18n.getMessage('copy_failed_with_error', err.message));
      });

    pendingCopyRequest = null;
  }, 300); // 300毫秒延迟，给用户足够的时间完成多段选择
}

// 显示复制通知
function showCopyNotification(message, messageKey, messageParams) {
  // 保存原始消息对象，用于后续类型检测
  const originalMessage = message;
  let messageType = 'success'; // 默认类型

  // 调试信息：输出当前浏览器UI语言
  const browserUILanguage = chrome.i18n.getUILanguage();
  console.log('Current browser UI language:', browserUILanguage);

  // 测试 contentGetMessage 是否正常工作
  const testPlainText = window.contentGetMessage ? window.contentGetMessage('plain_text_format') : chrome.i18n.getMessage('plain_text_format');
  const testMarkdown = window.contentGetMessage ? window.contentGetMessage('markdown_format') : chrome.i18n.getMessage('markdown_format');
  const testCopiedChars = window.contentGetMessage ? window.contentGetMessage('copied_chars_format', [123, testPlainText]) : chrome.i18n.getMessage('copied_chars_format', [123, testPlainText]);
  console.log('Test i18n messages:', {
    plain_text_format: testPlainText,
    markdown_format: testMarkdown,
    copied_chars_format: testCopiedChars,
    current_ui_language: chrome.i18n.getUILanguage()
  });

  // 强制测试不同语言下的翻译
  console.log('Force testing translations:');
  console.log('plain_text_format in English should be "Plain Text":', window.contentGetMessage ? window.contentGetMessage('plain_text_format') : chrome.i18n.getMessage('plain_text_format'));

  // 直接在控制台输出所有可能的格式键的翻译
  console.log('All possible format keys translations:');
  console.log('plain_text_format:', window.contentGetMessage ? window.contentGetMessage('plain_text_format') : chrome.i18n.getMessage('plain_text_format'));
  console.log('markdown_format:', window.contentGetMessage ? window.contentGetMessage('markdown_format') : chrome.i18n.getMessage('markdown_format'));
  console.log('plain_text:', window.contentGetMessage ? window.contentGetMessage('plain_text') : chrome.i18n.getMessage('plain_text'));
  console.log('markdown:', window.contentGetMessage ? window.contentGetMessage('markdown') : chrome.i18n.getMessage('markdown'));

  // 获取当前语言设置并处理消息
  chrome.storage.sync.get(['language'], function(result) {
    let currentLanguage = result.language || 'auto';
    let displayLanguage = currentLanguage;

    // 如果是自动设置，则使用浏览器语言
    if (currentLanguage === 'auto') {
      // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
      const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
      console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

      // 优先使用原始浏览器语言
      if (rawNavigatorLanguage.startsWith('zh')) {
        if (rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
          displayLanguage = 'zh_TW';
        } else {
          displayLanguage = 'zh_CN';
        }
      } else if (browserUILanguage.startsWith('zh')) {
        // 如果原始浏览器语言不是中文，但Chrome API返回的是中文，再检查Chrome API的结果
        if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO')) {
          displayLanguage = 'zh_TW';
        } else {
          displayLanguage = 'zh_CN';
        }
      } else {
        // 如果不是中文，强制使用英文
        displayLanguage = 'en';
      }

      // 如果浏览器语言不是中文，但显示语言被设置为中文，强制使用英文
      if (displayLanguage !== 'en' && !rawNavigatorLanguage.startsWith('zh')) {
        console.log('检测到不一致，强制使用英语。当前设置:', displayLanguage, '浏览器语言:', rawNavigatorLanguage);
        displayLanguage = 'en';
      }

      console.log('最终使用的语言:', displayLanguage, '原始浏览器语言:', rawNavigatorLanguage);
    }

    // 强制设置为英文，用于测试
    // displayLanguage = 'en';

    console.log('Display language:', displayLanguage);

    // 处理消息
    let finalMessage = ''; // 最终显示的消息

    // 处理新的消息格式
    if (typeof originalMessage === 'object' && originalMessage !== null) {
      if (originalMessage.type) {
        // 如果消息对象中包含类型信息，直接使用
        messageType = originalMessage.type;
      }

      if (originalMessage.useI18n && originalMessage.messageKey) {
        // 使用 i18n 消息
        const params = originalMessage.messageParams || [];

        // 调试信息：输出消息键和参数
        console.log('Getting i18n message for key:', originalMessage.messageKey, 'with params:', params);

        try {
          // 如果是 copied_chars_format 消息，使用直接构建消息
          if (originalMessage.messageKey === 'copied_chars_format' && params.length >= 2) {
            const count = params[0];
            let formatKey = params[1];

            console.log('Processing copied_chars_format with params:', params);

            // 确保我们使用的是消息键而不是已翻译的文本
            // 如果传入的是消息键，直接使用；如果是文本，尝试确定对应的消息键
            let actualFormatKey = formatKey;
            if (formatKey !== 'plain_text' && formatKey !== 'markdown') {
              if (formatKey === "Plain Text" || formatKey === "纯文本" || formatKey === "plain_text_format") {
                actualFormatKey = 'plain_text';
              } else if (formatKey === "Markdown" || formatKey === "markdown_format") {
                actualFormatKey = 'markdown';
              }
            }

            console.log('Using format key:', actualFormatKey);

            // 获取格式的翻译
            const translatedFormat = window.contentGetMessage ? window.contentGetMessage(actualFormatKey) : chrome.i18n.getMessage(actualFormatKey);
            console.log('Translated format:', translatedFormat, 'for key:', actualFormatKey);

            // 直接使用硬编码的消息模板，确保显示正确
            console.log('Using direct hardcoded template based on language:', displayLanguage);

            // 首先尝试使用 contentGetMessage 获取格式名称的翻译
            let formatName = window.contentGetMessage ? window.contentGetMessage(actualFormatKey) : chrome.i18n.getMessage(actualFormatKey);
            console.log('Format name from i18n (notification):', formatName, 'for key:', actualFormatKey, 'language:', displayLanguage);

            // 检查翻译是否符合预期语言
            let isCorrectLanguage = true;
            if (displayLanguage.startsWith('zh')) {
              // 对于中文，检查翻译是否包含中文字符
              if (!/[\u4e00-\u9fa5]/.test(formatName)) {
                isCorrectLanguage = false;
                console.log('Translation does not contain Chinese characters, falling back to hardcoded values (notification)');
              }
            } else {
              // 对于英文，检查翻译是否不包含中文字符
              if (/[\u4e00-\u9fa5]/.test(formatName)) {
                isCorrectLanguage = false;
                console.log('Translation contains Chinese characters for non-Chinese language, falling back to hardcoded values (notification)');
              }
            }

            // 如果翻译不符合预期语言，使用硬编码的翻译
            if (!isCorrectLanguage) {
              if (displayLanguage.startsWith('zh')) {
                if (displayLanguage === 'zh_TW') {
                  formatName = actualFormatKey === 'markdown' ? 'Markdown' : '純文本';
                } else {
                  formatName = actualFormatKey === 'markdown' ? 'Markdown' : '纯文本';
                }
              } else {
                formatName = actualFormatKey === 'markdown' ? 'Markdown' : 'Plain Text';
              }
              console.log('Using hardcoded format name (notification):', formatName);
            }

            // 构建通知消息
            if (displayLanguage.startsWith('zh')) {
              if (displayLanguage === 'zh_TW') {
                finalMessage = `已複製 ${count} 個字符 (${formatName})`;
              } else {
                finalMessage = `已复制 ${count} 个字符 (${formatName})`;
              }
            } else {
              finalMessage = `Copied ${count} characters (${formatName})`;
            }

            console.log('Final format name (notification):', formatName, 'for language:', displayLanguage, 'format key:', actualFormatKey);

            console.log('Final message:', finalMessage);

            console.log('Manually constructed message:', finalMessage);
          } else {
            // 对于其他消息，使用 chrome.i18n.getMessage
            // 检查参数是否是对象形式
            if (params && typeof params === 'object' && !Array.isArray(params)) {
              // 如果是对象形式，需要转换为 Chrome i18n API 需要的数组形式
              console.log('Converting object params to array:', params);
              const arrayParams = [];
              // 遍历对象的所有属性
              for (const key in params) {
                if (params.hasOwnProperty(key)) {
                  // 根据属性名找到对应的占位符索引
                  if (key === 'count' || key === 'COUNT') {
                    arrayParams[0] = params[key];
                  } else if (key === 'format' || key === 'FORMAT') {
                    arrayParams[1] = params[key];
                  } else if (key === 'error' || key === 'ERROR') {
                    arrayParams[0] = params[key];
                  } else if (key === 'site' || key === 'SITE') {
                    arrayParams[0] = params[key];
                  }
                  // 可以根据需要添加更多的映射
                }
              }
              console.log('Converted params to array:', arrayParams);
              finalMessage = window.contentGetMessage ? window.contentGetMessage(originalMessage.messageKey, arrayParams) : chrome.i18n.getMessage(originalMessage.messageKey, arrayParams);
            } else {
              // 如果是数组形式，直接使用
              console.log('Using array params directly:', params);

              // 尝试直接使用 Chrome 的 i18n API
              const chromeMessage = chrome.i18n.getMessage(originalMessage.messageKey, params);
              console.log('Chrome i18n API result:', chromeMessage);

              // 尝试使用 contentGetMessage
              const contentMessage = window.contentGetMessage ? window.contentGetMessage(originalMessage.messageKey, params) : null;
              console.log('contentGetMessage result:', contentMessage);

              // 使用最终结果
              finalMessage = contentMessage || chromeMessage || originalMessage.messageKey;

              // 特殊处理需要替换占位符的消息
              if (params.length > 0 && finalMessage) {
                console.log('检查消息是否需要替换占位符:', finalMessage);

                // 检查消息中是否包含未替换的占位符
                if (finalMessage.includes('$1') || finalMessage.includes('$COUNT$')) {
                  console.log('检测到未替换的占位符，手动替换');

                  // 替换 $1, $2, ... 占位符
                  for (let i = 0; i < params.length; i++) {
                    const placeholder = `$${i+1}`;
                    if (finalMessage.includes(placeholder)) {
                      finalMessage = finalMessage.replace(new RegExp('\\' + placeholder, 'g'), params[i]);
                    }
                  }

                  // 替换 $COUNT$ 占位符（特殊情况）
                  if (finalMessage.includes('$COUNT$') && params.length > 0) {
                    finalMessage = finalMessage.replace(/\$COUNT\$/g, params[0]);
                  }

                  console.log('替换后的消息:', finalMessage);
                }
              }
            }
            console.log('Got i18n message:', finalMessage);
          }
        } catch (error) {
          console.error('Error getting i18n message:', error);
          finalMessage = originalMessage.messageKey;
        }

        // 如果获取失败，记录错误并使用默认消息
        if (!finalMessage) {
          console.error(`Failed to get i18n message for key: ${originalMessage.messageKey}`);
          finalMessage = originalMessage.messageKey;
        }
      } else {
        // 使用传统格式
        finalMessage = originalMessage.text || '';
      }
    } else if (messageKey) {
      // 支持直接传递 messageKey 和 messageParams 的调用方式
      console.log('Getting i18n message for key:', messageKey, 'with params:', messageParams || []);
      finalMessage = window.contentGetMessage ? window.contentGetMessage(messageKey, messageParams || []) : chrome.i18n.getMessage(messageKey, messageParams || []);
    } else if (typeof originalMessage === 'string') {
      // 如果是字符串，直接使用
      finalMessage = originalMessage;
    }

    // 确保我们有一个消息要显示
    if (!finalMessage) {
      console.error('No message to display in notification');
      return;
    }

    // 显示通知
    displayNotification(finalMessage, messageType);
  });

  return; // 提前返回，等待异步获取语言设置
}

// 显示通知的实际函数
function displayNotification(message, messageType) {

  // 检查是否已存在通知元素
  let notification = document.getElementById('enhanced-tab-copy-notification');

  if (!notification) {
    // 创建通知元素
    notification = document.createElement('div');
    notification.id = 'enhanced-tab-copy-notification';

    // 设置样式
    Object.assign(notification.style, {
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      backgroundColor: 'rgba(26, 115, 232, 0.95)',
      color: 'white',
      padding: '12px 20px',
      borderRadius: '8px',
      zIndex: '9999',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      transition: 'all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28)',
      opacity: '0',
      transform: 'translateY(20px)',
      pointerEvents: 'none',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      display: 'flex',
      alignItems: 'center'
    });

    // 添加图标
    const icon = document.createElement('span');
    Object.assign(icon.style, {
      marginRight: '10px',
      fontSize: '18px'
    });
    icon.textContent = '✓';

    // 添加文本容器
    const textContainer = document.createElement('div');
    textContainer.className = 'notification-text';

    notification.appendChild(icon);
    notification.appendChild(textContainer);

    // 确保document.body存在，否则使用document.documentElement
    if (document.body) {
      document.body.appendChild(notification);
    } else if (document.documentElement) {
      document.documentElement.appendChild(notification);
    } else {
      console.warn('[SmartCopy] 无法添加通知，document.body和document.documentElement都不存在');
      // 返回函数，避免后续操作
      return;
    }
  }

  // 更新通知内容
  const textContainer = notification.querySelector('.notification-text');
  if (textContainer) {
    textContainer.textContent = message;
  } else {
    notification.textContent = message;
  }

  // 根据消息类型设置不同的颜色
  // 使用更可靠的方式检测消息类型，不依赖于特定语言的文本
  let isError = false;
  let isDisabled = false;

  // 首先检查是否有明确指定的类型
  if (messageType === 'error') {
    isError = true;
  } else if (messageType === 'disabled') {
    isDisabled = true;
  } else if (messageType === 'success') {
    // 使用默认成功样式
  } else {
    // 如果没有明确的类型，使用多语言关键词检测
    const lowerMessage = message.toLowerCase();
    isError = lowerMessage.includes('fail') || lowerMessage.includes('error') ||
              lowerMessage.includes('失败') || lowerMessage.includes('错误') ||
              lowerMessage.includes('失敗');
    isDisabled = !isError && (lowerMessage.includes('disable') || lowerMessage.includes('禁用') ||
                lowerMessage.includes('禁用') || lowerMessage.includes('disabled'));
  }

  // 根据消息类型设置样式
  if (isError) {
    notification.style.backgroundColor = 'rgba(217, 48, 37, 0.95)';
    notification.querySelector('span').textContent = '✗';
  } else if (isDisabled) {
    notification.style.backgroundColor = 'rgba(95, 99, 104, 0.95)';
    notification.querySelector('span').textContent = '✓';
  } else {
    notification.style.backgroundColor = 'rgba(26, 115, 232, 0.95)';
    notification.querySelector('span').textContent = '✓';
  }

  // 显示通知
  notification.style.opacity = '1';
  notification.style.transform = 'translateY(0)';

  // 2.5秒后隐藏通知
  setTimeout(() => {
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(20px)';

    // 完全隐藏后移除元素
    setTimeout(() => {
      if (notification && notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 2500);
}

// 处理快捷键
function handleKeyboardShortcuts(event) {
  console.log('键盘事件触发:', event.key, '按下，Alt键状态:', event.altKey, 'keyCode:', event.keyCode, 'which:', event.which, 'code:', event.code);

  // 检查是否是我们的快捷键
  // Alt+1: 复制当前标签页 (keyCode 49 或 numpad 97)
  if (event.altKey && (event.keyCode === 49 || event.keyCode === 97 || event.key === '1')) {
    console.log('检测到Alt+1快捷键');
    event.preventDefault();
    event.stopPropagation();

    // 直接显示通知，确认事件被捕获
    showCopyNotification(chrome.i18n.getMessage('copying_current_tab', '正在复制当前标签页...'));

    try {
      chrome.runtime.sendMessage({ action: 'copySingleTab' }, (response) => {
        // 检查是否发生错误
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
          showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', 'Extension context invalidated'));
          return;
        }

        console.log('copySingleTab响应:', response);
        if (response && response.success) {
          showCopyNotification(chrome.i18n.getMessage('copied_current_tab'));
        } else {
          showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', (response?.error || '未知错误')));
        }
      });
    } catch (err) {
      console.error('发送消息失败:', err);
      showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', err.message));
    }
    return false;
  }

  // Alt+2: 复制所有标签页 (keyCode 50 或 numpad 98)
  else if (event.altKey && (event.keyCode === 50 || event.keyCode === 98 || event.key === '2')) {
    console.log('检测到Alt+2快捷键');
    event.preventDefault();
    event.stopPropagation();

    // 直接显示通知，确认事件被捕获
    showCopyNotification(chrome.i18n.getMessage('copying_all_tabs'));

    try {
      chrome.runtime.sendMessage({ action: 'copyAllTabs' }, (response) => {
        // 检查是否发生错误
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
          showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', 'Extension context invalidated'));
          return;
        }

        console.log('copyAllTabs响应:', response);
        if (response && response.success) {
          showCopyNotification(chrome.i18n.getMessage('copied_all_tabs'));
        } else {
          showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', (response?.error || '未知错误')));
        }
      });
    } catch (err) {
      console.error('发送消息失败:', err);
      showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', err.message));
    }
    return false;
  }

  // Alt+3: 切换自动复制功能 (keyCode 51 或 numpad 99)
  else if (event.altKey && (event.keyCode === 51 || event.keyCode === 99 || event.key === '3')) {
    console.log('检测到Alt+3快捷键');
    event.preventDefault();
    event.stopPropagation();

    // 直接显示通知，确认事件被捕获
    showCopyNotification(chrome.i18n.getMessage('toggling_auto_copy'));

    try {
      chrome.runtime.sendMessage({ action: 'toggleAutoCopy' }, (response) => {
        // 检查是否发生错误
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
          showCopyNotification(chrome.i18n.getMessage('toggle_failed', 'Extension context invalidated'));
          return;
        }

        console.log('toggleAutoCopy响应:', response);
        if (response && response.success) {
          const status = response.enabled ?
            chrome.i18n.getMessage('auto_copy_enabled') :
            chrome.i18n.getMessage('auto_copy_disabled');
          showCopyNotification(status);

          // 如果当前设置与响应中的设置不一致，直接更新本地设置
          if (settings.autoTextCopy !== response.enabled) {
            settings.autoTextCopy = response.enabled;

            // 更新事件监听器
            if (settings.autoTextCopy) {
              document.removeEventListener('mouseup', handleTextSelection);
              document.addEventListener('mouseup', handleTextSelection);
              console.log('已启用自动复制功能');
            } else {
              document.removeEventListener('mouseup', handleTextSelection);
              console.log('已禁用自动复制功能');
            }
          }
        } else {
          showCopyNotification(chrome.i18n.getMessage('toggle_failed', (response?.error || '未知错误')));
        }
      });
    } catch (err) {
      console.error('发送消息失败:', err);
      showCopyNotification(chrome.i18n.getMessage('toggle_failed', err.message));
    }
    return false;
  }

  // Alt+4: 打开剪贴板历史记录 (keyCode 52 或 numpad 100)
  else if (event.altKey && (event.keyCode === 52 || event.keyCode === 100 || event.key === '4')) {
    console.log('检测到Alt+4快捷键');
    event.preventDefault();
    event.stopPropagation();

    // 直接显示通知，确认事件被捕获
    showCopyNotification(chrome.i18n.getMessage('opening_clipboard_history'));

    try {
      chrome.runtime.sendMessage({ action: 'openClipboardHistory' }, response => {
        if (chrome.runtime.lastError) {
          console.error('发送消息失败:', chrome.runtime.lastError);
          // 尝试直接打开剪贴板历史记录页面
          try {
            window.open(chrome.runtime.getURL('clipboard-history.html'), '_blank');
          } catch (err) {
            console.error('打开剪贴板历史记录失败:', err);
            showCopyNotification(chrome.i18n.getMessage('open_clipboard_history_failed', ''));
          }
        }
      });
    } catch (err) {
      console.error('发送消息失败:', err);
      showCopyNotification(chrome.i18n.getMessage('open_clipboard_history_failed', err.message));
    }
    return false;
  }

  // Alt+5: 切换超级复制功能 (keyCode 53 或 numpad 101)
  else if (event.altKey && (event.keyCode === 53 || event.keyCode === 101 || event.key === '5')) {
    console.log('检测到Alt+5快捷键');
    event.preventDefault();
    event.stopPropagation();

    // 切换超级复制功能
    superCopyEnabled = !superCopyEnabled;

    if (superCopyEnabled) {
      applySuperCopy();
      // 使用国际化消息显示通知，明确指定消息类型为success
      showCopyNotification({
        messageKey: 'super_copy_enabled_notification',
        useI18n: true,
        type: 'success'
      });

      // 如果启用了记忆功能，记住这个网站
      if (settings.superCopyMemory) {
        unblockMemory.add(window.location.href);
      }
    } else {
      // 使用国际化消息显示通知，明确指定消息类型为disabled
      showCopyNotification({
        messageKey: 'super_copy_disabled_notification',
        useI18n: true,
        type: 'disabled'
      });
    }

    // 更新设置
    try {
      chrome.storage.sync.set({ superCopy: superCopyEnabled });

      // 通知其他组件
      chrome.runtime.sendMessage({
        action: 'settingsUpdated',
        settings: { superCopy: superCopyEnabled }
      }).catch(err => {
        console.error('发送设置更新消息失败:', err);
      });
    } catch (err) {
      console.error('保存超级复制设置失败:', err);
    }

    return false;
  }

  // 检测 Ctrl+V 或 Command+V，确保粘贴功能
  if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
    console.log('检测到粘贴快捷键');
    event.preventDefault();
    event.stopPropagation();
    return handlePaste(null, chrome.i18n.getMessage('shortcut'));
  }
}

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 检查浏览器语言变化
  if (message.action === 'checkBrowserLanguage') {
    // 接收到检查浏览器语言的消息
    console.log('接收到检查浏览器语言的消息');

    // 获取当前浏览器UI语言
    const browserUILanguage = chrome.i18n.getUILanguage();
    console.log('当前浏览器UI语言:', browserUILanguage);

    // 获取当前语言设置
    chrome.storage.sync.get(['language'], (result) => {
      const storedLanguage = result.language || 'auto';

      // 如果设置为自动，则重新应用国际化消息
      if (storedLanguage === 'auto') {
        let displayLanguage = 'en';

        // 直接获取原始浏览器语言，不依赖 chrome.i18n.getUILanguage()
        const rawNavigatorLanguage = navigator.language || navigator.userLanguage;
        console.log('原始浏览器语言 (navigator.language):', rawNavigatorLanguage);

        if (browserUILanguage.startsWith('zh') || rawNavigatorLanguage.startsWith('zh')) {
          if (browserUILanguage.includes('TW') || browserUILanguage.includes('HK') || browserUILanguage.includes('MO') ||
              rawNavigatorLanguage.includes('TW') || rawNavigatorLanguage.includes('HK') || rawNavigatorLanguage.includes('MO')) {
            displayLanguage = 'zh_TW';
          } else {
            displayLanguage = 'zh_CN';
          }
        } else {
          // 对于所有非中文语言，强制使用英语
          displayLanguage = 'en';
        }

        console.log('自动检测语言:', displayLanguage, 'Chrome API 浏览器语言:', browserUILanguage, '原始浏览器语言:', rawNavigatorLanguage);

        // 通知背景脚本更新语言设置
        chrome.runtime.sendMessage({
          action: 'languageDetected',
          detectedLanguage: displayLanguage
        }).catch(() => {
          // 忽略错误
        });
      }
    });

    if (sendResponse) {
      sendResponse({ success: true });
    }
    return true;
  }

  // 支持官方快捷键命令消息
  if (message.action === 'toggleSuperCopy') {
    // 只在状态真正变化时切换
    const prevState = !!superCopyEnabled;
    const nextState = !prevState;
    if (prevState === nextState) {
      sendResponse && sendResponse({ success: true, enabled: superCopyEnabled });
      return true;
    }
    superCopyEnabled = nextState;
    chrome.storage.sync.set({ superCopy: superCopyEnabled });
    if (superCopyEnabled) {
      applySuperCopy();
      // 使用国际化消息显示通知，明确指定消息类型为success
      showCopyNotification({
        messageKey: 'super_copy_enabled_notification',
        useI18n: true,
        type: 'success'
      });
    } else {
      unregisterSuperCopyEvents();
      // 使用国际化消息显示通知，明确指定消息类型为disabled
      showCopyNotification({
        messageKey: 'super_copy_disabled_notification',
        useI18n: true,
        type: 'disabled'
      });
    }
    sendResponse && sendResponse({ success: true, enabled: superCopyEnabled });
    return true;
  }
  if (message.action === 'toggleAutoCopy') {
    settings.autoTextCopy = !settings.autoTextCopy;
    chrome.storage.sync.set({ autoTextCopy: settings.autoTextCopy });
    // 使用新的国际化消息格式显示通知
    showCopyNotification({
      messageKey: settings.autoTextCopy ? 'auto_copy_enabled' : 'auto_copy_disabled',
      useI18n: true,
      type: settings.autoTextCopy ? 'success' : 'disabled'
    });
    sendResponse && sendResponse({ success: true, enabled: settings.autoTextCopy });
    return true;
  }

  // 首先检查扩展上下文是否有效
  if (!isExtensionContextValid()) {
    console.error('Extension context invalidated');
    return false;
  }

  if (message.action === 'showCopyNotification') {
    // 支持新的消息格式
    showCopyNotification(message);
    sendResponse({ success: true });
    return true;
  }

  if (message.action === 'updateSettings') {
    // 更新设置
    settings = { ...settings, ...message.settings };
    console.log('更新设置:', settings);

    // 特别处理选中文字复制格式设置
    if (message.settings.hasOwnProperty('copyTextFormat')) {
      console.log('更新选中文字复制格式设置:', message.settings.copyTextFormat);

      // 确保设置被正确保存
      chrome.storage.sync.set({ copyTextFormat: message.settings.copyTextFormat })
        .then(() => {
          console.log('选中文字复制格式设置已保存到存储');

          // 验证设置是否保存成功
          return chrome.storage.sync.get(['copyTextFormat']);
        })
        .then((result) => {
          console.log('验证选中文字复制格式设置:', result.copyTextFormat);

          if (result.copyTextFormat !== message.settings.copyTextFormat) {
            console.warn('选中文字复制格式设置保存失败，重试...');
            return chrome.storage.sync.set({ copyTextFormat: message.settings.copyTextFormat });
          }
        })
        .catch(error => {
          console.error('保存选中文字复制格式设置失败:', error);
        });
    }

    // 根据设置更新事件监听器
    if (message.settings.hasOwnProperty('autoTextCopy')) {
      if (settings.autoTextCopy) {
        // 先移除再添加，避免重复
        document.removeEventListener('mouseup', handleTextSelection);
        document.addEventListener('mouseup', handleTextSelection);
        // 启用 selectionchange 自动复制（带防抖）
        // selectionchange 自动复制逻辑始终独立于超级复制
        // 每次设置变更都重新绑定，确保立即生效
        if (!window._autoCopySelectionChangeHandler) {
          let lastSelection = '';
          let selectionDebounceTimer = null;
          window._autoCopySelectionChangeHandler = function () {
            // 只依赖 autoTextCopy
            if (!settings.autoTextCopy) return;

            // 如果鼠标按下状态，说明用户正在选择文本，暂不处理
            if (isMouseDown) return;

            const selection = window.getSelection();
            if (!selection) return;
            const selectedText = selection.toString().trim();
            // 排除输入框/可编辑区域
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) return;

            // 检查选中文本是否达到最小字符数
            if (selectedText && selectedText.length >= (settings.minCharacters || 1)) {
              // 只有当文本与上次选择不同时才更新lastSelection并复制
              if (selectedText !== lastSelection) {
                lastSelection = selectedText;

                // 延迟处理，避免在多段选择过程中多次触发
                if (mouseUpDelayTimer) {
                  clearTimeout(mouseUpDelayTimer);
                }

                mouseUpDelayTimer = setTimeout(() => {
                  copyTextToClipboard(selectedText);
                  mouseUpDelayTimer = null;
                }, 100);
              }
            }
          };
          window._autoCopySelectionChangeDebounced = function() {
            if (selectionDebounceTimer) clearTimeout(selectionDebounceTimer);
            selectionDebounceTimer = setTimeout(window._autoCopySelectionChangeHandler, 200);
          };
        }
        // 每次都解绑再绑定，确保切换设置后立即生效
        document.removeEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
        document.addEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
        console.log('已启用自动复制功能（含 selectionchange）');
      } else {
        document.removeEventListener('mouseup', handleTextSelection);
        // 禁用 selectionchange 自动复制
        if (window._autoCopySelectionChangeDebounced) {
          document.removeEventListener('selectionchange', window._autoCopySelectionChangeDebounced);
        }
        console.log('已禁用自动复制功能');
      }

      // 如果 showNotification 为 true，则显示通知
      if (message.showNotification && message.notificationMessage) {
        showCopyNotification(message.notificationMessage);
      }
    }

    // 处理超级复制设置变更
    if (message.settings.hasOwnProperty('superCopy')) {
      superCopyEnabled = message.settings.superCopy;

      if (superCopyEnabled) {
        applySuperCopy();
        // 如果启用了记忆功能，记住这个网站
        if (settings.superCopyMemory) {
          unblockMemory.add(window.location.href);
        }
      }
    }

    sendResponse({ success: true });
    return true;
  }

  if (message.action === 'copyToClipboard') {
    if (message.text) {
      // 确保换行符被正确保留
      navigator.clipboard.writeText(message.text)
        .then(() => {
          showCopyNotification(chrome.i18n.getMessage('copied_to_clipboard'));
          // 在发送响应前检查扩展上下文是否仍然有效
          if (isExtensionContextValid()) {
            sendResponse({ success: true });
          }
        })
        .catch(err => {
          console.error('复制失败:', err);
          showCopyNotification(chrome.i18n.getMessage('copy_failed_with_error', err.message));
          // 在发送响应前检查扩展上下文是否仍然有效
          if (isExtensionContextValid()) {
            sendResponse({ success: false, error: err.message });
          }
        });
      return true; // 保持消息通道开放，以便异步响应
    }
  }

  if (message.action === 'openClipboardHistory') {
    chrome.runtime.sendMessage({ action: 'openClipboardHistory' });
    sendResponse({ success: true });
    return true;
  }
});

// 检查扩展上下文是否有效
function isExtensionContextValid() {
  try {
    // 尝试访问 runtime API
    return chrome.runtime && chrome.runtime.id;
  } catch (e) {
    return false;
  }
}

// -------- CSP兼容内容脚本解锁方案 --------
// 创建事件处理程序的引用，便于后续移除
// 定义事件处理函数引用，便于后续移除
let cspEventHandlers = [];

// 注册 CSP 兼容解锁方案
function registerCSPCompatibleUnlock() {
  // 如果超级复制未启用，不执行任何操作
  if (!superCopyEnabled) {
    console.log(`[超级复制] 超级复制已禁用，不注册 CSP 兼容解锁`);
    return;
  }

  console.log(`[超级复制] 注册 CSP 兼容解锁方案`);

  // 1. 移除所有 on* 属性
  ['oncopy','oncut','onpaste','oncontextmenu','onselectstart','onmousedown','onmouseup','onkeydown','onkeypress','ondragstart'].forEach(attr => {
    document.querySelectorAll('[' + attr + ']').forEach(el => el.removeAttribute(attr));
  });

  // 2. 捕获阶段拦截常见限制事件
  ['copy','cut','paste','contextmenu','selectstart','keydown','keypress','dragstart'].forEach(event => {
    const handler = function(e) {
      // 如果超级复制未启用，不拦截事件
      if (!superCopyEnabled) {
        return true;
      }

      e.stopImmediatePropagation();
      // 只对 copy 事件做特殊处理
      if (event === 'copy') {
        e.preventDefault();
        const text = window.getSelection().toString();
        if (e.clipboardData) {
          e.clipboardData.setData('text/plain', text);
        }
      }
    };

    // 添加事件处理程序并保存引用
    document.addEventListener(event, handler, true);
    cspEventHandlers.push({ event, handler });
  });

  // 特殊处理 mousedown、mouseup 和 click 事件，避免干扰浮动按钮的拖动和点击功能
  ['mousedown', 'mouseup', 'click'].forEach(event => {
    const handler = function(e) {
      // 如果超级复制未启用，不拦截事件
      if (!superCopyEnabled) {
        return true;
      }

      // 检查事件目标是否是浮动按钮或其子元素
      const floatingBtn = document.getElementById('smartcopy-floating-btn');
      if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
        // 如果是浮动按钮，不拦截事件
        console.log(`[超级复制] 检测到浮动按钮的${event}事件，不拦截`);
        return true;
      }

      // 对于点击事件，我们需要特别小心，确保不会阻止浮动按钮的点击
      if (event === 'click') {
        // 再次检查是否是浮动按钮的点击
        if (floatingBtn && (e.target === floatingBtn || floatingBtn.contains(e.target))) {
          console.log(`[超级复制] 检测到浮动按钮的点击事件，不拦截`);
          return true;
        }
      }

      e.stopImmediatePropagation();
    };

    // 添加事件处理程序并保存引用
    document.addEventListener(event, handler, true);
    cspEventHandlers.push({ event, handler });
  });

  console.log(`[超级复制] CSP 兼容解锁方案已注册`);
}

// 移除 CSP 兼容解锁方案
function removeCSPCompatibleUnlock() {
  console.log(`[超级复制] 移除 CSP 兼容解锁方案`);

  // 移除所有事件处理程序
  cspEventHandlers.forEach(({ event, handler }) => {
    document.removeEventListener(event, handler, true);
  });

  // 清空处理程序数组
  cspEventHandlers = [];

  console.log(`[超级复制] CSP 兼容解锁方案已移除`);
}

// 根据超级复制状态决定是否注册 CSP 兼容解锁方案
if (superCopyEnabled) {
  registerCSPCompatibleUnlock();
} else {
  console.log(`[超级复制] 超级复制已禁用，不注册 CSP 兼容解锁`);
}

// 添加强制允许选中的样式
function addUserSelectStyle() {
  // 如果超级复制未启用，不添加样式
  if (!superCopyEnabled) {
    console.log(`[超级复制] 超级复制已禁用，不添加强制选中样式`);
    return;
  }

  console.log(`[超级复制] 添加强制允许选中的样式`);

  const styleId = 'super-copy-user-select-style';

  // 检查样式是否已经存在，避免重复添加
  if (document.getElementById(styleId)) {
    return;
  }

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    * {
      -webkit-user-select: text !important;
      user-select: text !important;
    }
  `;

  if (document.head) {
    document.head.appendChild(style);
  } else if (document.documentElement) {
    document.documentElement.appendChild(style);
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      (document.head || document.documentElement || document.body).appendChild(style);
    });
  }
}

// 移除强制允许选中的样式
function removeUserSelectStyle() {
  const styleId = 'super-copy-user-select-style';
  const styleElement = document.getElementById(styleId);

  if (styleElement) {
    console.log(`[超级复制] 移除强制允许选中的样式`);
    styleElement.remove();
  }
}

// 根据超级复制状态添加或移除样式
if (superCopyEnabled) {
  addUserSelectStyle();
} else {
  removeUserSelectStyle();
}

// 监听来自浮动按钮的自定义事件 - 已禁用，改用 updateSiteSettings 消息
/* 注释掉自定义事件处理程序，避免重复通知
document.addEventListener('smartcopy-toggle-supercopy', function(event) {
  console.log('收到浮动按钮切换超级复制功能的事件:', event.detail);

  // 获取事件中的域名和状态
  const domain = event.detail.domain;
  const newEnabled = event.detail.enabled;
  const currentDomain = getDomain();

  // 无需检查域名匹配，直接处理所有事件
  // 这样可以确保同一网站的所有页面都会响应设置变化
  logger.debug(`当前域名: ${currentDomain}, 事件域名: ${domain}, 新状态: ${newEnabled ? '已启用' : '已禁用'}`);

  // 更新当前状态
  superCopyEnabled = newEnabled;

  // 根据新状态应用或移除超级复制功能
  if (superCopyEnabled) {
    registerSuperCopyEvents();
    applySuperCopy();
    // 显示通知，明确指定消息类型为success
    showCopyNotification({
      messageKey: 'super_copy_enabled_notification',
      useI18n: true,
      type: 'success'
    });
    console.log('[超级复制] 已启用超级复制功能');
  } else {
    unregisterSuperCopyEvents();
    // 显示通知，明确指定消息类型为disabled
    showCopyNotification({
      messageKey: 'super_copy_disabled_notification',
      useI18n: true,
      type: 'disabled'
    });
    console.log('[超级复制] 已禁用超级复制功能');
  }
});
*/

// 监听来自浮动按钮的初始化状态事件 - 已禁用，改用存储变化监听器
/* 注释掉初始化状态事件处理程序，避免重复通知
document.addEventListener('smartcopy-init-state', function(event) {
  console.log('收到浮动按钮初始化状态事件:', event.detail);

  // 获取事件中的域名和状态
  const domain = event.detail.domain;
  const currentDomain = getDomain();

  // 无论浮动按钮的状态如何，默认都设置为禁用状态
  // 这样确保页面第一次加载时超级复制功能是禁用的
  superCopyEnabled = false;
  console.log(`[超级复制] 初始化状态同步 - 当前域名: ${currentDomain}, 事件域名: ${domain}, 状态设置为禁用`);

  // 禁用超级复制功能
  unregisterSuperCopyEvents();
  console.log('[超级复制] 初始化时禁用超级复制功能');

  // 清除可能存在的状态提示
  const copyNotifications = document.querySelectorAll('.smartcopy-notification');
  copyNotifications.forEach(notification => {
    notification.remove();
  });
});
*/

// 清除可能存在的状态提示
function clearAllNotifications() {
  const copyNotifications = document.querySelectorAll('.smartcopy-notification');
  copyNotifications.forEach(notification => {
    notification.remove();
  });
}

// 监听来自浮动按钮的切换请求
document.addEventListener('smartcopy-toggle-request', async function(event) {
  console.log('收到浮动按钮切换超级复制功能的请求');

  // 防止重复处理
  if (window.superCopyToggleInProgress) {
    console.log('[超级复制] 切换操作正在进行中，忽略此次请求');
    return;
  }

  // 设置切换操作进行中标志
  window.superCopyToggleInProgress = true;

  // 获取当前域名
  const domain = getDomain();
  let newEnabled;

  try {
    // 检查是否有浮动按钮传递的状态信息
    if (event.detail && typeof event.detail.buttonState === 'boolean') {
      // 直接使用浮动按钮传递的状态
      newEnabled = event.detail.buttonState;
      console.log(`[超级复制] 使用浮动按钮传递的状态: ${newEnabled ? '已启用' : '已禁用'}`);
    } else {
      // 如果没有状态信息，则从存储中获取并切换
      try {
        const data = await chrome.storage.sync.get(['superCopySiteSettings']);
        const siteSettings = data.superCopySiteSettings || {};

        // 检查当前域名的超级复制状态
        let currentEnabled = false;
        if (domain in siteSettings) {
          currentEnabled = Boolean(siteSettings[domain]);
        }

        // 切换状态
        newEnabled = !currentEnabled;
        console.log(`[超级复制] 从存储中获取并切换状态为: ${newEnabled ? '已启用' : '已禁用'}`);
      } catch (storageError) {
        // 如果无法访问存储，则使用当前内存中的状态并切换
        console.warn(`[超级复制] 无法访问存储，使用当前内存中的状态:`, storageError);
        newEnabled = !superCopyEnabled;
        console.log(`[超级复制] 使用当前内存中的状态并切换为: ${newEnabled ? '已启用' : '已禁用'}`);
      }
    }

    // 尝试更新存储，但如果失败也不中断功能
    try {
      const data = await chrome.storage.sync.get(['superCopySiteSettings']);
      const siteSettings = data.superCopySiteSettings || {};

      // 更新存储
      siteSettings[domain] = newEnabled;
      await chrome.storage.sync.set({ superCopySiteSettings: siteSettings });
      console.log(`[超级复制] 成功更新存储`);
    } catch (storageError) {
      console.warn(`[超级复制] 无法更新存储，但将继续应用功能状态:`, storageError);
    }

    // 更新当前状态
    superCopyEnabled = newEnabled;

    // 根据新状态应用或移除超级复制功能
    if (superCopyEnabled) {
      // 清理之前的状态，确保干净应用
      cleanupSuperCopy();

      // 注册事件处理程序
      registerSuperCopyEvents();

      // 立即应用超级复制功能
      applySuperCopy();

      // 应用绕过技术到整个文档
      applySuperCopyBypass();

      // 设置MutationObserver以监视DOM变化
      setupSuperCopyObserver();

      // 处理特定网站的保护
      handleSpecificProtections();

      console.log(`[超级复制] 已完全启用当前网站的超级复制功能: ${domain}`);

      // 只有当状态真正从禁用变为启用时，才显示启用通知
      if (event.detail && event.detail.buttonState === true) {
        // 使用国际化消息显示通知，明确指定消息类型为success
        showCopyNotification({
          messageKey: 'super_copy_enabled_notification',
          useI18n: true,
          type: 'success'
        });
      }
    } else {
      // 彻底清理超级复制功能
      unregisterSuperCopyEvents();
      cleanupSuperCopy();
      console.log(`[超级复制] 已彻底禁用当前网站的超级复制功能: ${domain}`);

      // 只有当状态真正从启用变为禁用时，才显示禁用通知
      if (event.detail && event.detail.buttonState === false) {
        // 使用国际化消息显示通知，明确指定消息类型为disabled
        showCopyNotification({
          messageKey: 'super_copy_disabled_notification',
          useI18n: true,
          type: 'disabled'
        });
      }
    }

    // 通知浮动按钮更新状态
    document.dispatchEvent(new CustomEvent('smartcopy-state-changed', {
      detail: {
        domain: domain,
        enabled: newEnabled
      }
    }));

    // 添加多个延迟通知，确保浮动按钮状态与实际状态一致
    const sendStateUpdate = () => {
      document.dispatchEvent(new CustomEvent('smartcopy-state-changed', {
        detail: {
          domain: domain,
          enabled: superCopyEnabled
        }
      }));
      console.log(`[超级复制] 发送状态更新，确保浮动按钮状态一致: ${superCopyEnabled ? '已启用' : '已禁用'}`);
    };

    // 设置多个延迟通知，确保状态最终一致
    setTimeout(sendStateUpdate, 100);
    setTimeout(sendStateUpdate, 300);
    setTimeout(sendStateUpdate, 500);

    // 最后一次通知后，重置切换操作进行中标志
    setTimeout(() => {
      window.superCopyToggleInProgress = false;
      console.log('[超级复制] 切换操作完成，重置切换操作进行中标志');
    }, 600);

  } catch (error) {
    console.error(`[超级复制] 切换超级复制功能时出错:`, error);
    showCopyNotification(chrome.i18n.getMessage('super_copy_toggle_error', error.message));
    // 出错时也要重置切换操作进行中标志
    window.superCopyToggleInProgress = false;
  }

  // 设置一个安全超时，确保标志最终会被重置
  setTimeout(() => {
    if (window.superCopyToggleInProgress) {
      console.log('[超级复制] 安全超时触发，重置切换操作进行中标志');
      window.superCopyToggleInProgress = false;
    }
  }, 2000);
});

// 监听浮动按钮请求当前超级复制状态
document.addEventListener('smartcopy-request-state', async function() {
  console.log('收到浮动按钮请求当前超级复制状态');

  const domain = getDomain();

  try {
    // 获取当前设置
    const data = await chrome.storage.sync.get(['superCopySiteSettings']);
    const siteSettings = data.superCopySiteSettings || {};

    // 检查当前域名的超级复制状态
    let enabled = false;
    if (domain in siteSettings) {
      enabled = Boolean(siteSettings[domain]);
    }

    console.log(`[超级复制] 当前状态: ${enabled ? '已启用' : '已禁用'}`);

    // 通知浮动按钮当前状态
    document.dispatchEvent(new CustomEvent('smartcopy-state-changed', {
      detail: {
        domain: domain,
        enabled: enabled
      }
    }));

  } catch (error) {
    console.error(`[超级复制] 获取超级复制状态时出错:`, error);
  }
});

// 恢复超级复制功能的通知效果，添加回被删除的超级复制初始化和消息处理代码
(async function initSuperCopyPerSite() {
  const domain = getDomain();
  logger.debug('初始化', `初始化网站设置，当前域名: ${domain}`);

  try {
    // 直接获取所有站点设置
    const data = await chrome.storage.sync.get(['superCopySiteSettings']);
    logger.debug('初始化', `读取到的设置:`, JSON.stringify(data));

    // 检查当前域名的设置
    if (data.superCopySiteSettings && typeof data.superCopySiteSettings === 'object' && domain in data.superCopySiteSettings) {
      superCopyEnabled = Boolean(data.superCopySiteSettings[domain]);
    } // 否则保持 superCopyEnabled 的当前值

    logger.debug('初始化', `找到当前域名的设置: ${superCopyEnabled ? '已启用' : '已禁用'}`);

    // 根据超级复制状态应用或清理功能
    if (superCopyEnabled) {
      logger.debug('初始化', `当前网站超级复制已启用，注册相关事件`);
      registerSuperCopyEvents();
    } else {
      logger.debug('初始化', `当前网站超级复制已禁用，清理相关事件`);
      unregisterSuperCopyEvents();
      // 确保彻底清理
      cleanupSuperCopy();
    }
  } catch (error) {
    console.error(`[超级复制] 读取设置时出错:`, error);
    // 出错时保持当前状态，不设置新的默认值
    logger.debug('初始化', `出错，保持当前状态: ${superCopyEnabled ? '已启用' : '已禁用'}`);
  }

  // 应用设置
  logger.debug('初始化', `应用设置: ${superCopyEnabled ? '已启用' : '已禁用'}`);
  if (superCopyEnabled) {
    registerSuperCopyEvents();
    applySuperCopy(); // 确保应用超级复制样式
    logger.debug('初始化', `已启用超级复制事件处理`);
  } else {
    unregisterSuperCopyEvents();
    logger.debug('初始化', `已禁用超级复制事件处理`);
  }

  // 监听存储变化，实现跨页面设置同步
  chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'sync' && changes.superCopySiteSettings) {
      logger.debug('设置变化', `检测到超级复制设置变化:`, changes.superCopySiteSettings);

      const newSettings = changes.superCopySiteSettings.newValue || {};
      const currentDomain = getDomain();

      logger.debug('设置变化', `当前域名: ${currentDomain}, 检查是否有相关设置`);

      // 检查当前域名的设置是否存在
      if (currentDomain in newSettings) {
        const newEnabled = Boolean(newSettings[currentDomain]);
        logger.debug('设置变化', `找到当前域名的新设置: ${newEnabled ? '已启用' : '已禁用'}`);

        // 如果状态发生变化，则更新
        if (newEnabled !== superCopyEnabled) {
          logger.debug('设置变化', `状态发生变化，从 ${superCopyEnabled ? '已启用' : '已禁用'} 变为 ${newEnabled ? '已启用' : '已禁用'}`);

          // 更新当前状态
          superCopyEnabled = newEnabled;

          // 应用新设置
          if (superCopyEnabled) {
            registerSuperCopyEvents();
            applySuperCopy();
            // 使用国际化消息显示通知，明确指定消息类型为success
            showCopyNotification({
              messageKey: 'super_copy_enabled_notification',
              useI18n: true,
              type: 'success'
            });
          } else {
            unregisterSuperCopyEvents();
            removeUserSelectStyle();
            // 使用国际化消息显示通知，明确指定消息类型为disabled
            showCopyNotification({
              messageKey: 'super_copy_disabled_notification',
              useI18n: true,
              type: 'disabled'
            });
          }
        } else {
          logger.debug('设置变化', `状态未变化，保持 ${superCopyEnabled ? '已启用' : '已禁用'}`);
        }
      } else {
        logger.debug('设置变化', `新设置中没有当前域名 ${currentDomain} 的设置`);
      }
    }
  });
})();

// 监听设置变更，动态切换
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  logger.debug('消息处理', `收到消息:`, msg);

  // 处理超级复制站点设置变化
  if (msg.action === 'superCopySiteSettingsChanged' && msg.settings) {
    logger.debug('消息处理', `收到超级复制站点设置变化:`, msg.settings);

    // 获取当前域名
    const currentDomain = getDomain();
    logger.debug('消息处理', `当前域名: ${currentDomain}`);

    // 检查当前域名的设置是否存在
    if (currentDomain in msg.settings) {
      // 获取新的设置状态
      const newEnabled = Boolean(msg.settings[currentDomain]);
      logger.debug('消息处理', `新的超级复制状态: ${newEnabled ? '已启用' : '已禁用'}`);

      // 如果状态发生变化，则更新
      if (newEnabled !== superCopyEnabled) {
        logger.debug('消息处理', `状态发生变化，从 ${superCopyEnabled ? '已启用' : '已禁用'} 变为 ${newEnabled ? '已启用' : '已禁用'}`);

        // 更新当前状态
        superCopyEnabled = newEnabled;

        // 应用新设置
        if (superCopyEnabled) {
          registerSuperCopyEvents();
          applySuperCopy();
          // 使用国际化消息显示通知，明确指定消息类型为success
          showCopyNotification({
            messageKey: 'super_copy_enabled_notification',
            useI18n: true,
            type: 'success'
          });
        } else {
          unregisterSuperCopyEvents();
          removeUserSelectStyle();
          // 使用国际化消息显示通知，明确指定消息类型为disabled
          showCopyNotification({
            messageKey: 'super_copy_disabled_notification',
            useI18n: true,
            type: 'disabled'
          });
        }
      } else {
        logger.debug('消息处理', `状态未变化，保持 ${superCopyEnabled ? '已启用' : '已禁用'}`);
      }
    }

    // 发送响应
    if (sendResponse) {
      sendResponse({ success: true });
    }
    return true;
  }

  // 处理单个站点超级复制设置变化
  if (msg.action === 'superCopySettingChanged') {
    logger.debug('消息处理', `收到超级复制设置变化:`, msg);

    // 获取当前域名和目标域名
    const currentDomain = getDomain();
    const targetDomain = msg.domain;

    // 如果当前域名与目标域名相同，则应用设置
    if (msg.domain === currentDomain) {
      logger.debug('消息处理', `域名匹配，应用新设置: ${msg.enabled ? '已启用' : '已禁用'}`);

      // 更新当前网站的超级复制状态
      superCopyEnabled = msg.enabled;
      logger.debug('消息处理', `更新当前网站设置为: ${superCopyEnabled ? '已启用' : '已禁用'}`);

      // 应用设置
      if (superCopyEnabled) {
        registerSuperCopyEvents();
        applySuperCopy();
        showCopyNotification(chrome.i18n.getMessage('super_copy_enabled'));
      } else {
        unregisterSuperCopyEvents();
        removeUserSelectStyle();
        showCopyNotification(chrome.i18n.getMessage('super_copy_disabled_notification'));
      }
    } else {
      logger.debug('消息处理', `域名不匹配，当前: ${currentDomain}, 目标: ${targetDomain}`);
    }

    // 发送响应
    if (sendResponse) {
      sendResponse({ success: true, applied: true });
    }
    return true;
  }

  // 处理全局设置更新
  if (msg.action === 'updateSettings' && msg.settings && typeof msg.settings.superCopy === 'boolean') {
    superCopyEnabled = msg.settings.superCopy;
    logger.debug('消息处理', `更新全局设置为: ${superCopyEnabled ? '已启用' : '已禁用'}`);

    if (superCopyEnabled) {
      // 清理之前的状态，确保干净应用
      cleanupSuperCopy();

      // 注册事件处理程序
      registerSuperCopyEvents();

      // 立即应用超级复制功能
      applySuperCopy();

      // 应用绕过技术到整个文档
      applySuperCopyBypass();

      // 设置MutationObserver以监视DOM变化
      setupSuperCopyObserver();

      // 处理特定网站的保护
      handleSpecificProtections();

      logger.debug('消息处理', `已完全启用超级复制功能`);

      // 显示成功消息，使用对象格式并明确指定类型
      showStatusMessage({
        messageKey: 'super_copy_enabled_with_details',
        useI18n: true,
        type: 'success'
      });
    } else {
      // 彻底清理超级复制功能
      unregisterSuperCopyEvents();
      cleanupSuperCopy();
      logger.debug('消息处理', `已彻底禁用超级复制功能`);

      // 显示禁用消息，使用对象格式并明确指定类型
      showStatusMessage({
        messageKey: 'super_copy_disabled_notification',
        useI18n: true,
        type: 'disabled'
      });
    }

    // 返回响应
    if (sendResponse) {
      sendResponse({ success: true });
    }
    return true; // 保持消息通道打开
  }

  // 处理站点级别设置更新
  if (msg.action === 'updateSiteSettings' && typeof msg.enabled === 'boolean' && msg.domain) {
    const currentDomain = getDomain();
    logger.debug('消息处理', `站点设置更新，消息域名: ${msg.domain}, 当前域名: ${currentDomain}`);

    // 只处理当前域名的设置
    if (msg.domain === currentDomain) {
      // 更新当前网站的超级复制状态
      superCopyEnabled = msg.enabled;
      logger.debug('消息处理', `更新当前网站设置为: ${superCopyEnabled ? '已启用' : '已禁用'}`);

      // 应用设置
      if (superCopyEnabled) {
        registerSuperCopyEvents();
        applySuperCopy();
        // 使用国际化消息显示通知，明确指定消息类型为success
        showCopyNotification({
          messageKey: 'super_copy_enabled_notification',
          useI18n: true,
          type: 'success'
        });
      } else {
        unregisterSuperCopyEvents();
        removeUserSelectStyle();
        // 使用国际化消息显示通知，明确指定消息类型为disabled
        showCopyNotification({
          messageKey: 'super_copy_disabled_notification',
          useI18n: true,
          type: 'disabled'
        });
      }
    } else {
      logger.debug('消息处理', `域名不匹配，忽略设置更新`);
      if (sendResponse) {
        sendResponse({ success: true, applied: false, reason: 'domain_mismatch' });
      }
    }
    return true; // 保持消息通道打开
  }
});
