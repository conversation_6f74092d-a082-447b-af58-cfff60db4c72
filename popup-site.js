import { getDomain } from './siteUtils.js';

/**
 * 获取当前域名的超级复制开关状态（优先site设置，无则用全局superCopy）
 */
export async function getSuperCopyEnabledForCurrentSite() {
  const domain = getDomain();
  const settings = await chrome.storage.sync.get(['superCopy', 'superCopySiteSettings']);
  if (settings.superCopySiteSettings && typeof settings.superCopySiteSettings[domain] === 'boolean') {
    return settings.superCopySiteSettings[domain];
  }
  // 无单独设置时用全局默认
  return settings.superCopy !== false;
}

/**
 * 设置当前域名的超级复制开关
 * @param {boolean} enabled
 */
export async function setSuperCopyForCurrentSite(enabled) {
  const domain = getDomain();
  const settings = await chrome.storage.sync.get(['superCopySiteSettings']);
  const siteSettings = settings.superCopySiteSettings || {};
  siteSettings[domain] = enabled;
  await chrome.storage.sync.set({ superCopySiteSettings: siteSettings });
  return siteSettings;
}
