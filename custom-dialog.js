/**
 * 自定义对话框模块
 * 提供自定义的确认对话框，替代浏览器原生的confirm()函数
 * 这样可以完全控制对话框的标题和内容，解决国际化问题
 */

// 创建自定义确认对话框
function createCustomConfirmDialog(title, message, confirmText, cancelText) {
  return new Promise((resolve) => {
    // 创建对话框容器
    const overlay = document.createElement('div');
    overlay.className = 'custom-dialog-overlay';
    
    // 创建对话框
    const dialog = document.createElement('div');
    dialog.className = 'custom-dialog';
    
    // 创建对话框标题
    const header = document.createElement('div');
    header.className = 'custom-dialog-header';
    header.textContent = title;
    
    // 创建对话框内容
    const content = document.createElement('div');
    content.className = 'custom-dialog-content';
    content.textContent = message;
    
    // 创建按钮容器
    const buttons = document.createElement('div');
    buttons.className = 'custom-dialog-buttons';
    
    // 创建取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.className = 'custom-dialog-button cancel';
    cancelButton.textContent = cancelText || '取消';
    cancelButton.addEventListener('click', () => {
      document.body.removeChild(overlay);
      resolve(false);
    });
    
    // 创建确认按钮
    const confirmButton = document.createElement('button');
    confirmButton.className = 'custom-dialog-button confirm';
    confirmButton.textContent = confirmText || '确定';
    confirmButton.addEventListener('click', () => {
      document.body.removeChild(overlay);
      resolve(true);
    });
    
    // 组装对话框
    buttons.appendChild(cancelButton);
    buttons.appendChild(confirmButton);
    dialog.appendChild(header);
    dialog.appendChild(content);
    dialog.appendChild(buttons);
    overlay.appendChild(dialog);
    
    // 添加到页面
    document.body.appendChild(overlay);
    
    // 聚焦确认按钮
    confirmButton.focus();
  });
}

// 导出函数
window.customConfirmDialog = createCustomConfirmDialog;
