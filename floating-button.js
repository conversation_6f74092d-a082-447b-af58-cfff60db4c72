// 用于在页面上显示一个可拖动的浮动按钮，以便用户快速开启/关闭超级复制功能

// 创建样式表
function createFloatingButtonStyles() {
  // 检查是否已存在样式
  if (document.getElementById('smartcopy-floating-styles')) {
    return;
  }

  const styleEl = document.createElement('style');
  styleEl.id = 'smartcopy-floating-styles';
  styleEl.textContent = `
    #smartcopy-floating-btn {
      position: fixed;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(66, 133, 244, 0.9);
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: move;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      z-index: 2147483647;
      user-select: none;
      transition: background-color 0.3s ease;
      right: 20px;
      bottom: 80px;
      font-size: 14px;
      font-weight: bold;
      font-family: Arial, sans-serif;
      padding: 5px;
      touch-action: none;
    }

    #smartcopy-floating-btn:hover {
      opacity: 0.9;
    }

    #smartcopy-floating-btn.dragging {
      opacity: 0.7;
    }

    #smartcopy-floating-status {
      position: fixed;
      bottom: 140px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      z-index: 2147483647;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    #smartcopy-floating-status.show {
      opacity: 1;
    }
  `;

  document.head.appendChild(styleEl);
}

// 创建浮动按钮元素
function createFloatingButton() {
  // 添加样式表
  createFloatingButtonStyles();

  // 先移除可能存在的浮动按钮
  const existingButton = document.getElementById('smartcopy-floating-btn');
  if (existingButton) {
    existingButton.remove();
  }

  // 创建按钮容器
  const buttonContainer = document.createElement('div');
  buttonContainer.id = 'smartcopy-floating-btn';

  // 设置标题，确保使用正确的语言
  if (window.contentGetMessage) {
    // 强制刷新语言设置
    setTimeout(() => {
      buttonContainer.title = window.contentGetMessage('floating_button_title');
      console.log('浮动按钮标题已设置为:', buttonContainer.title);
    }, 100); // 短暂延迟以确保语言设置已加载
  } else {
    buttonContainer.title = chrome.i18n.getMessage('floating_button_title');
  }

  // 初始设置为灰色（默认禁用状态）
  buttonContainer.style.backgroundColor = 'rgba(128, 128, 128, 0.9)';

  // 创建文本内容，分两行显示
  // 确保使用正确的语言，优先使用contentGetMessage，如果不可用则使用chrome.i18n
  if (window.contentGetMessage) {
    // 强制刷新语言设置
    setTimeout(() => {
      buttonContainer.innerHTML = window.contentGetMessage('floating_button_text');
      console.log('浮动按钮文本已设置为:', buttonContainer.innerHTML);
    }, 100); // 短暂延迟以确保语言设置已加载
  } else {
    buttonContainer.innerHTML = chrome.i18n.getMessage('floating_button_text');
  }

  // 添加到页面
  document.body.appendChild(buttonContainer);

  // 加载保存的位置
  loadButtonPosition(buttonContainer);

  logger.debug('浮动按钮', '浮动按钮已创建，位置：右下角');

  return buttonContainer;
}

// 加载按钮位置
function loadButtonPosition(button) {
  chrome.storage.sync.get('floatingButtonPosition')
    .then(data => {
      if (data.floatingButtonPosition) {
        const pos = data.floatingButtonPosition;
        // 如果有保存的位置，则应用它
        if (pos.right) button.style.right = pos.right;
        if (pos.bottom) button.style.bottom = pos.bottom;
        logger.debug('浮动按钮', '已加载保存的位置', pos);
      }
    })
    .catch(err => {
      console.error('加载按钮位置失败:', err);
    });
}

// 保存按钮位置
function saveButtonPosition(button) {
  const position = {
    right: button.style.right,
    bottom: button.style.bottom
  };

  chrome.storage.sync.set({ floatingButtonPosition: position })
    .then(() => {
      logger.debug('浮动按钮', '位置已保存', position);
    })
    .catch(err => {
      console.error('保存按钮位置失败:', err);
    });
}

// 更新按钮状态
function updateButtonState(enabled) {
  const button = document.getElementById('smartcopy-floating-btn');
  if (!button) return;

  if (enabled) {
    button.style.backgroundColor = 'rgba(66, 133, 244, 0.9)';
    // 设置启用状态的标题，确保使用正确的语言
    if (window.contentGetMessage) {
      // 强制刷新语言设置
      setTimeout(() => {
        button.title = window.contentGetMessage('floating_button_enabled');
        console.log('浮动按钮启用状态标题已设置为:', button.title);
      }, 50); // 短暂延迟以确保语言设置已加载
    } else {
      button.title = chrome.i18n.getMessage('floating_button_enabled');
    }
  } else {
    button.style.backgroundColor = 'rgba(128, 128, 128, 0.9)';
    // 设置禁用状态的标题，确保使用正确的语言
    if (window.contentGetMessage) {
      // 强制刷新语言设置
      setTimeout(() => {
        button.title = window.contentGetMessage('floating_button_disabled');
        console.log('浮动按钮禁用状态标题已设置为:', button.title);
      }, 50); // 短暂延迟以确保语言设置已加载
    } else {
      button.title = chrome.i18n.getMessage('floating_button_disabled');
    }
  }
}

// 实现拖动功能
function makeButtonDraggable(button) {
  // 添加一个全局变量，用于跟踪是否发生了拖动
  window.buttonWasDragged = false;

  let isDragging = false;
  let clickStartTime = 0;
  let clickTimeout;

  // 初始化拖动参数
  let startX, startY;
  let startRight, startBottom;

  // 鼠标按下事件 - 开始拖动
  button.addEventListener('mousedown', function(e) {
    // 防止默认行为
    e.preventDefault();
    e.stopPropagation();

    // 记录点击开始时间，用于区分点击和拖动
    clickStartTime = Date.now();

    // 添加一个标志，表示这是一个潜在的拖动操作
    button._potentialDrag = true;

    // 记录初始位置
    startX = e.clientX;
    startY = e.clientY;

    // 获取当前按钮的右下角位置
    const rect = button.getBoundingClientRect();
    startRight = window.innerWidth - rect.right;
    startBottom = window.innerHeight - rect.bottom;

    // 添加移动和释放事件 - 使用捕获阶段，确保在任何情况下都能捕获到事件
    document.addEventListener('mousemove', handleMouseMove, true);
    document.addEventListener('mouseup', handleMouseUp, true);

    // 清除可能存在的点击超时
    clearTimeout(clickTimeout);
  });

  // 鼠标移动事件
  function handleMouseMove(e) {
    // 如果移动超过3像素，认为是拖动而非点击
    const moveX = Math.abs(e.clientX - startX);
    const moveY = Math.abs(e.clientY - startY);

    if (moveX > 3 || moveY > 3) {
      isDragging = true;
      window.buttonWasDragged = true; // 设置全局拖动标志
      button.classList.add('dragging');

      // 计算新位置
      const deltaX = startX - e.clientX;
      const deltaY = startY - e.clientY;

      // 更新按钮位置
      const newRight = startRight + deltaX;
      const newBottom = startBottom + deltaY;

      // 限制范围
      button.style.right = `${Math.max(0, newRight)}px`;
      button.style.bottom = `${Math.max(0, newBottom)}px`;

      // 确保使用右下角定位
      button.style.left = 'auto';
      button.style.top = 'auto';

      // 记录拖动日志
      logger.debug('浮动按钮', `拖动中，移动距离: X=${moveX}, Y=${moveY}`);
    }
  }

  // 鼠标释放事件
  function handleMouseUp(e) {
    // 移除事件监听器 - 确保使用捕获阶段
    document.removeEventListener('mousemove', handleMouseMove, true);
    document.removeEventListener('mouseup', handleMouseUp, true);

    // 计算从mousedown到mouseup的时间差
    const clickDuration = Date.now() - clickStartTime;

    // 记录当前的拖动状态
    const wasDragged = isDragging || window.buttonWasDragged;

    // 检查是否是拖动操作
    if (isDragging) {
      // 重置拖动状态
      isDragging = false;
      button.classList.remove('dragging');

      // 保存新位置
      saveButtonPosition(button);

      logger.debug('浮动按钮', '拖动结束，新位置已保存');

      // 拖动操作不触发点击事件，但保持全局拖动标志
      // window.buttonWasDragged 会在点击事件处理程序中检查并重置

      // 设置一个延迟，确保拖动标志在一段时间后被重置
      // 这样可以防止拖动后立即点击时的问题
      setTimeout(() => {
        window.buttonWasDragged = false;
        logger.debug('浮动按钮', '拖动标志已自动重置');
      }, 300);

      return;
    }

    // 检查是否是潜在的拖动操作
    if (button._potentialDrag) {
      // 如果点击持续时间超过200毫秒，认为是拖动操作
      if (clickDuration > 200) {
        logger.debug('浮动按钮', '检测到长按操作，不触发点击事件');

        // 设置全局拖动标志
        window.buttonWasDragged = true;

        // 保存新位置，以防万一有微小移动
        saveButtonPosition(button);

        // 重置潜在拖动标志
        button._potentialDrag = false;

        // 设置一个延迟，确保拖动标志在一段时间后被重置
        setTimeout(() => {
          window.buttonWasDragged = false;
          logger.debug('浮动按钮', '拖动标志已自动重置');
        }, 300);

        // 长按操作不触发点击事件
        return;
      }

      // 如果鼠标位置与初始位置不同，认为是微小拖动
      const moveX = Math.abs(e.clientX - startX);
      const moveY = Math.abs(e.clientY - startY);

      if (moveX > 1 || moveY > 1) {
        logger.debug('浮动按钮', '检测到微小拖动，不触发点击事件');

        // 设置全局拖动标志
        window.buttonWasDragged = true;

        // 保存新位置，以防万一有微小移动
        saveButtonPosition(button);

        // 重置潜在拖动标志
        button._potentialDrag = false;

        // 设置一个延迟，确保拖动标志在一段时间后被重置
        setTimeout(() => {
          window.buttonWasDragged = false;
          logger.debug('浮动按钮', '拖动标志已自动重置');
        }, 300);

        // 微小拖动不触发点击事件
        return;
      }
    }

    // 如果是纯粹的点击操作（而非拖动）
    if (!wasDragged) {
      // 添加小延迟以确保这是点击而非拖动的开始
      clickTimeout = setTimeout(() => {
        // 确保在触发点击事件前，拖动标志已被重置
        window.buttonWasDragged = false;

        // 模拟点击事件
        button.dispatchEvent(new Event('click'));
      }, 50); // 增加延迟，确保状态已稳定
    } else {
      logger.debug('浮动按钮', '检测到拖动操作，不触发点击事件');

      // 设置一个延迟，确保拖动标志在一段时间后被重置
      setTimeout(() => {
        window.buttonWasDragged = false;
        logger.debug('浮动按钮', '拖动标志已自动重置');
      }, 300);
    }

    // 重置潜在拖动标志
    button._potentialDrag = false;
  }

  // 触摸设备支持
  button.addEventListener('touchstart', function(e) {
    const touch = e.touches[0];

    // 添加一个标志，表示这是一个潜在的拖动操作
    button._potentialDrag = true;

    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    button.dispatchEvent(mouseEvent);
    e.preventDefault(); // 防止缩放等行为
    e.stopPropagation(); // 阻止事件传播
  }, { passive: false, capture: true }); // 使用捕获阶段

  document.addEventListener('touchmove', function(e) {
    // 检查是否有触摸点
    if (e.touches.length === 0) return;

    const touch = e.touches[0];

    // 计算移动距离
    const moveX = Math.abs(touch.clientX - startX);
    const moveY = Math.abs(touch.clientY - startY);

    // 如果移动超过3像素，认为是拖动而非点击
    if (moveX > 3 || moveY > 3) {
      isDragging = true;
      window.buttonWasDragged = true; // 设置全局拖动标志
      button.classList.add('dragging');

      // 记录拖动日志
      logger.debug('浮动按钮', `触摸拖动中，移动距离: X=${moveX}, Y=${moveY}`);
    }

    // 只有在拖动状态下才移动按钮
    if (isDragging) {
      // 计算新位置
      const deltaX = startX - touch.clientX;
      const deltaY = startY - touch.clientY;

      // 更新按钮位置
      const newRight = startRight + deltaX;
      const newBottom = startBottom + deltaY;

      // 限制范围
      button.style.right = `${Math.max(0, newRight)}px`;
      button.style.bottom = `${Math.max(0, newBottom)}px`;

      // 确保使用右下角定位
      button.style.left = 'auto';
      button.style.top = 'auto';
    }

    e.preventDefault();
    e.stopPropagation(); // 阻止事件传播
  }, { passive: false, capture: true }); // 使用捕获阶段

  document.addEventListener('touchend', function(e) {
    // 如果是拖动操作，不触发点击事件
    if (isDragging) {
      // 重置拖动状态
      isDragging = false;
      button.classList.remove('dragging');

      // 保存新位置
      saveButtonPosition(button);

      logger.debug('浮动按钮', '触摸拖动结束，新位置已保存');

      // 阻止事件传播
      e.preventDefault();
      e.stopPropagation();

      // 重置潜在拖动标志
      button._potentialDrag = false;

      // 设置一个延迟，确保拖动标志在一段时间后被重置
      setTimeout(() => {
        window.buttonWasDragged = false;
        logger.debug('浮动按钮', '触摸拖动标志已自动重置');
      }, 300);

      return;
    }

    // 计算从touchstart到touchend的时间差
    const touchDuration = Date.now() - clickStartTime;

    // 如果是潜在的拖动操作
    if (button._potentialDrag) {
      // 如果触摸持续时间超过200毫秒，认为是拖动操作
      if (touchDuration > 200) {
        logger.debug('浮动按钮', '检测到长按触摸操作，不触发点击事件');

        // 设置全局拖动标志
        window.buttonWasDragged = true;

        // 保存新位置，以防万一有微小移动
        saveButtonPosition(button);

        // 阻止事件传播
        e.preventDefault();
        e.stopPropagation();

        // 重置潜在拖动标志
        button._potentialDrag = false;

        // 设置一个延迟，确保拖动标志在一段时间后被重置
        setTimeout(() => {
          window.buttonWasDragged = false;
          logger.debug('浮动按钮', '长按触摸标志已自动重置');
        }, 300);

        return;
      }
    }

    // 如果是纯粹的点击操作，才触发mouseup事件
    // 确保在触发点击事件前，拖动标志已被重置
    window.buttonWasDragged = false;

    // 添加小延迟，确保状态已稳定
    setTimeout(() => {
      const mouseEvent = new MouseEvent('mouseup', {});
      document.dispatchEvent(mouseEvent);
    }, 50);

    e.preventDefault();
    e.stopPropagation(); // 阻止事件传播
  }, { passive: false, capture: true }); // 使用捕获阶段

  // 防止默认拖动行为
  button.addEventListener('dragstart', function(e) {
    e.preventDefault();
    e.stopPropagation(); // 阻止事件传播
    return false;
  }, true); // 使用捕获阶段

  // 自动避开其他元素的功能
  function avoidCollision() {
    // 获取按钮当前位置
    const rect = button.getBoundingClientRect();

    // 检查页面上是否有固定定位的元素与我们的按钮重叠
    const fixedElements = Array.from(document.querySelectorAll('*')).filter(el => {
      if (el === button || !el.offsetParent) return false;

      const style = window.getComputedStyle(el);
      return style.position === 'fixed' && style.zIndex && parseInt(style.zIndex) > 1000;
    });

    // 如果发现重叠元素，尝试移动按钮
    for (const el of fixedElements) {
      const elRect = el.getBoundingClientRect();

      // 检查是否重叠
      if (!(rect.right < elRect.left ||
          rect.left > elRect.right ||
          rect.bottom < elRect.top ||
          rect.top > elRect.bottom)) {

        // 如果重叠，移动按钮
        const rightOffset = parseInt(button.style.right || '20');
        button.style.right = (rightOffset + 20) + 'px';

        // 保存新位置
        saveButtonPosition(button);
        logger.debug('浮动按钮', '自动避开重叠元素');

        // 只移动一次，避免无限循环
        break;
      }
    }
  }

  // 页面加载完成后执行一次避免重叠
  if (document.readyState === 'complete') {
    setTimeout(avoidCollision, 1000);
  } else {
    window.addEventListener('load', () => setTimeout(avoidCollision, 1000));
  }

  // 窗口大小改变时执行避免重叠
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(avoidCollision, 500);
  });
}

// 初始化浮动按钮
async function initFloatingButton() {
  // 首先检查用户设置是否启用了浮动按钮
  try {
    const settings = await chrome.storage.sync.get({showFloatingButton: false});
    logger.debug('浮动按钮', `获取设置结果: ${JSON.stringify(settings)}`);

    // 如果设置为禁用浮动按钮，则不显示
    if (settings.showFloatingButton === false) {
      logger.debug('浮动按钮', '设置为不显示浮动按钮，移除可能存在的按钮');
      // 移除可能存在的浮动按钮
      const existingButton = document.getElementById('smartcopy-floating-btn');
      if (existingButton) {
        existingButton.remove();
        logger.debug('浮动按钮', '已移除现有浮动按钮');
      }
      return;
    } else {
      logger.debug('浮动按钮', '设置为显示浮动按钮');
    }
  } catch (error) {
    logger.error('浮动按钮', '获取浮动按钮设置失败:', error);
    // 出错时默认显示按钮
  }

  // 检查是否已经创建了浮动按钮
  if (document.getElementById('smartcopy-floating-btn')) {
    logger.debug('浮动按钮', '浮动按钮已存在，不重复创建');
    return;
  }

  // 创建按钮
  const button = createFloatingButton();
  if (!button) return;

  // 确保按钮初始位置在右下角
  button.style.right = '20px';
  button.style.bottom = '80px';
  button.style.left = 'auto';
  button.style.top = 'auto';

  // 默认设置为禁用状态
  updateButtonAppearance(button, false);

  // 从内容脚本获取初始状态
  requestSuperCopyState();

  // 监听来自内容脚本的状态更新事件
  document.addEventListener('smartcopy-state-changed', function(event) {
    if (event.detail && typeof event.detail.enabled === 'boolean') {
      const button = document.getElementById('smartcopy-floating-btn');
      if (button) {
        updateButtonAppearance(button, event.detail.enabled);
        logger.debug('浮动按钮', `收到状态更新，新状态: ${event.detail.enabled ? '已启用' : '已禁用'}`);
      }
    }
  });

  // 添加点击事件处理程序
  button.onclick = function() {
    try {
      logger.debug('浮动按钮', '点击事件触发');

      // 检查是否是拖动操作
      if (window.buttonWasDragged) {
        logger.debug('浮动按钮', '检测到拖动操作，不触发状态切换');
        // 重置拖动标志
        window.buttonWasDragged = false;
        return;
      }

      // 防止重复点击
      if (window.buttonClickInProgress) {
        logger.debug('浮动按钮', '点击处理进行中，忽略此次点击');
        return;
      }

      // 设置点击处理进行中标志
      window.buttonClickInProgress = true;

      // 直接判断当前按钮的颜色状态
      // 使用计算后的样式而不是内联样式来判断
      const computedStyle = window.getComputedStyle(button);
      const bgColor = computedStyle.backgroundColor;
      const isCurrentlyEnabled = bgColor.includes('66, 133, 244') || bgColor.includes('rgb(66, 133, 244)');

      logger.debug('浮动按钮', `当前状态: ${isCurrentlyEnabled ? '已启用' : '已禁用'}, 背景色: ${bgColor}`);

      // 立即更新按钮外观，提供即时反馈
      const newState = !isCurrentlyEnabled;
      updateButtonAppearance(button, newState);
      logger.debug('浮动按钮', `已立即更新按钮外观为 ${newState ? '已启用(蓝色)' : '已禁用(灰色)'}`);

      // 创建一个自定义事件，带有当前按钮状态的信息
      const event = new CustomEvent('smartcopy-toggle-request', {
        detail: { buttonState: newState }
      });

      // 触发内容脚本中的超级复制切换功能
      document.dispatchEvent(event);

      // 添加多个延迟检查，确保状态一致性
      const checkAndUpdateState = () => {
        // 检查按钮状态是否与预期一致
        const currentStyle = window.getComputedStyle(button);
        const currentBgColor = currentStyle.backgroundColor;
        const currentEnabled = currentBgColor.includes('66, 133, 244') || currentBgColor.includes('rgb(66, 133, 244)');

        if (currentEnabled !== newState) {
          logger.debug('浮动按钮', `检测到按钮状态与预期不一致，强制更新外观`);
          updateButtonAppearance(button, newState);
          return false; // 状态不一致，返回false
        }
        return true; // 状态一致，返回true
      };

      // 设置多个检查点，确保状态最终一致
      setTimeout(() => {
        if (!checkAndUpdateState()) {
          // 如果第一次检查失败，再次尝试
          setTimeout(() => {
            if (!checkAndUpdateState()) {
              // 如果第二次检查失败，最后再尝试一次
              setTimeout(() => {
                checkAndUpdateState();
                // 最后一次检查后，无论结果如何，都重置点击处理进行中标志
                window.buttonClickInProgress = false;
              }, 300);
            } else {
              // 第二次检查成功，重置点击处理进行中标志
              window.buttonClickInProgress = false;
            }
          }, 200);
        } else {
          // 第一次检查成功，重置点击处理进行中标志
          window.buttonClickInProgress = false;
        }
      }, 100);

      // 设置一个安全超时，确保标志最终会被重置
      setTimeout(() => {
        window.buttonClickInProgress = false;
      }, 1000);

    } catch (error) {
      console.error(`浮动按钮: 切换超级复制功能时出错:`, error);
      // 出错时也要重置点击处理进行中标志
      window.buttonClickInProgress = false;
    } finally {
      // 确保重置拖动标志
      window.buttonWasDragged = false;
    }
  };

  // 使按钮可拖动
  makeButtonDraggable(button);

  logger.debug('浮动按钮', '浮动按钮初始化完成，位置：右下角');
}

// 请求当前超级复制状态
function requestSuperCopyState() {
  document.dispatchEvent(new CustomEvent('smartcopy-request-state'));
  logger.debug('浮动按钮', '已请求当前超级复制状态');
}

// 更新按钮外观
function updateButtonAppearance(button, enabled) {
  if (enabled) {
    button.style.backgroundColor = 'rgba(66, 133, 244, 0.9)';
    // 设置启用状态的标题，确保使用正确的语言
    if (window.contentGetMessage) {
      // 强制刷新语言设置
      setTimeout(() => {
        button.title = window.contentGetMessage('floating_button_enabled');
        // 同时更新按钮文本，确保语言正确
        button.innerHTML = window.contentGetMessage('floating_button_text');
        console.log('浮动按钮启用状态已更新，标题:', button.title, '文本:', button.innerHTML);
      }, 50); // 短暂延迟以确保语言设置已加载
    } else {
      button.title = chrome.i18n.getMessage('floating_button_enabled');
      button.innerHTML = chrome.i18n.getMessage('floating_button_text');
    }
    logger.debug('浮动按钮', '更新按钮外观为启用(蓝色)');
  } else {
    button.style.backgroundColor = 'rgba(128, 128, 128, 0.9)';
    // 设置禁用状态的标题，确保使用正确的语言
    if (window.contentGetMessage) {
      // 强制刷新语言设置
      setTimeout(() => {
        button.title = window.contentGetMessage('floating_button_disabled');
        // 同时更新按钮文本，确保语言正确
        button.innerHTML = window.contentGetMessage('floating_button_text');
        console.log('浮动按钮禁用状态已更新，标题:', button.title, '文本:', button.innerHTML);
      }, 50); // 短暂延迟以确保语言设置已加载
    } else {
      button.title = chrome.i18n.getMessage('floating_button_disabled');
      button.innerHTML = chrome.i18n.getMessage('floating_button_text');
    }
    logger.debug('浮动按钮', '更新按钮外观为禁用(灰色)');
  }
}

// 获取标准化的域名（仅返回主域名，使超级复制设置在同一网站所有页面共享）
function getDomain() {
  const host = window.location.hostname;

  // 提取主域名
  // 处理常见的情况，如 www.example.com, sub.example.com 等
  const domainRegex = /([\w-]+\.)?([\w-]+\.[\w-]+)$/;
  const matches = host.match(domainRegex);

  if (matches && matches[2]) {
    // 返回主域名，如 example.com
    return matches[2];
  }

  // 如果无法提取，返回原始域名
  return host;
}

// 在页面加载完成后初始化浮动按钮
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initFloatingButton);
} else {
  initFloatingButton();
}

// 监听设置变化，动态显示或隐藏浮动按钮
chrome.storage.onChanged.addListener(function(changes, areaName) {
  if (areaName === 'sync' && changes.showFloatingButton) {
    const newValue = changes.showFloatingButton.newValue;
    const oldValue = changes.showFloatingButton.oldValue;
    const existingButton = document.getElementById('smartcopy-floating-btn');

    logger.debug('浮动按钮', `设置变化: ${oldValue} -> ${newValue}`);

    if (newValue === true && !existingButton) {
      // 如果设置为显示浮动按钮，且当前没有按钮，则创建按钮
      logger.debug('浮动按钮', '设置已开启，创建浮动按钮');
      initFloatingButton();
    } else if (newValue === false && existingButton) {
      // 如果设置为隐藏浮动按钮，且当前有按钮，则移除按钮
      logger.debug('浮动按钮', '设置已关闭，移除浮动按钮');
      existingButton.remove();
    }
  }
});
