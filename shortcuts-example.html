<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SmartCopy 快捷键示例</title>
  <link rel="stylesheet" href="shortcuts.css">
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      background-color: #f5f5f5;
      color: #333;
      line-height: 1.6;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    h1 {
      color: #1a73e8;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .section {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    h2 {
      color: #1a73e8;
      margin-top: 0;
      margin-bottom: 15px;
    }
    
    p {
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <h1>SmartCopy 快捷键示例</h1>
  
  <div class="section">
    <h2>基本快捷键</h2>
    <p>以下是 SmartCopy 扩展的主要快捷键：</p>
    
    <div class="shortcuts-container">
      <div class="shortcut-row">
        <div class="shortcut-label">复制当前标签页</div>
        <div class="shortcut-combo">
          <span class="windows">
            <span class="key">Alt</span>
            <span class="key-plus">+</span>
            <span class="key">1</span>
            <span class="os-tag">(Windows)</span>
          </span>
          <span class="mac">
            <span class="key">Option</span>
            <span class="key-plus">+</span>
            <span class="key">1</span>
            <span class="os-tag">(Mac)</span>
          </span>
        </div>
      </div>
      
      <div class="shortcut-row">
        <div class="shortcut-label">复制所有标签页</div>
        <div class="shortcut-combo">
          <span class="windows">
            <span class="key">Alt</span>
            <span class="key-plus">+</span>
            <span class="key">2</span>
            <span class="os-tag">(Windows)</span>
          </span>
          <span class="mac">
            <span class="key">Option</span>
            <span class="key-plus">+</span>
            <span class="key">2</span>
            <span class="os-tag">(Mac)</span>
          </span>
        </div>
      </div>
      
      <div class="shortcut-row">
        <div class="shortcut-label">自动复制选中文本</div>
        <div class="shortcut-combo">
          <span class="windows">
            <span class="key">Alt</span>
            <span class="key-plus">+</span>
            <span class="key">3</span>
            <span class="os-tag">(Windows)</span>
          </span>
          <span class="mac">
            <span class="key">Option</span>
            <span class="key-plus">+</span>
            <span class="key">3</span>
            <span class="os-tag">(Mac)</span>
          </span>
        </div>
      </div>
      
      <div class="shortcut-row">
        <div class="shortcut-label">剪贴板历史记录</div>
        <div class="shortcut-combo">
          <span class="windows">
            <span class="key">Alt</span>
            <span class="key-plus">+</span>
            <span class="key">4</span>
            <span class="os-tag">(Windows)</span>
          </span>
          <span class="mac">
            <span class="key">Option</span>
            <span class="key-plus">+</span>
            <span class="key">4</span>
            <span class="os-tag">(Mac)</span>
          </span>
        </div>
      </div>
    </div>
  </div>
  
  <div class="section">
    <h2>蓝色主题样式</h2>
    <p>使用 <code>shortcuts-blue</code> 类可以应用蓝色主题：</p>
    
    <div class="shortcuts-container shortcuts-blue">
      <h3>快捷方式</h3>
      <div class="shortcut-row">
        <div class="shortcut-label">超级复制</div>
        <div class="shortcut-combo">
          <span class="windows">
            <span class="key">Alt</span>
            <span class="key-plus">+</span>
            <span class="key">5</span>
            <span class="os-tag">(Windows)</span>
          </span>
          <span class="mac">
            <span class="key">Option</span>
            <span class="key-plus">+</span>
            <span class="key">5</span>
            <span class="os-tag">(Mac)</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
