/* 自定义对话框样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90%;
  overflow: hidden;
  animation: dialog-appear 0.3s ease-out;
}

@keyframes dialog-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-dialog-header {
  background-color: #1a73e8;
  color: white;
  padding: 16px;
  font-size: 18px;
  font-weight: 500;
}

.custom-dialog-content {
  padding: 20px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}

.custom-dialog-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #eee;
}

.custom-dialog-button {
  padding: 8px 16px;
  margin-left: 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.custom-dialog-button.cancel {
  background-color: #f1f3f4;
  color: #5f6368;
}

.custom-dialog-button.cancel:hover {
  background-color: #e8eaed;
}

.custom-dialog-button.confirm {
  background-color: #1a73e8;
  color: white;
}

.custom-dialog-button.confirm:hover {
  background-color: #1765cc;
}
