<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Clipboard History</title>
  <!-- 标题将通过 i18n.js 脚本动态设置 -->
  <link rel="stylesheet" href="custom-dialog.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      background-color: #f5f5f5;
      color: #333;
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* 顶部导航栏 */
    .header {
      background-color: #1a73e8;
      color: white;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header h1 {
      font-size: 20px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }

    .header h1 .icon {
      margin-right: 10px;
      font-size: 24px;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }

    /* 主内容区 */
    .main-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-width: 100%;
      margin: 0 auto;
      padding: 0;
      width: 100%;
    }

    /* 过滤器和分页 */
    .toolbar {
      padding: 16px 20px;
      background-color: white;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
    }

    .filter-container {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .filter-container label {
      font-size: 14px;
      color: #5f6368;
    }

    .filter-container select {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: white;
      font-size: 14px;
      min-width: 100px;
    }

    /* 历史记录列表 */
    .history-list-container {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .history-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
    }

    /* 卡片样式 */
    .history-item {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.12);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transition: box-shadow 0.2s;
      height: 100%;
      position: relative;
      border-top: 4px solid #4CAF50;
    }

    .history-item:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* 根据来源设置不同的边框颜色 */
    .history-item.source-manual {
      border-top-color: #1a73e8;
    }

    .history-item.source-auto {
      border-top-color: #4CAF50;
    }

    .history-item.source-current-tab {
      border-top-color: #F9A825;
    }

    .history-item.source-all-tabs {
      border-top-color: #D81B60;
    }

    /* 卡片标题栏 */
    .item-title-bar {
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f1f1f1;
    }

    .title-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .source-tag {
      font-size: 12px;
      padding: 3px 8px;
      border-radius: 12px;
      background-color: #e8f0fe;
      color: #1a73e8;
      white-space: nowrap;
    }

    .source-tag.auto {
      background-color: #e6f4ea;
      color: #137333;
    }

    .source-tag.current-tab {
      background-color: #fff8e1;
      color: #b06000;
    }

    .source-tag.all-tabs {
      background-color: #fce4ec;
      color: #c2185b;
    }

    .timestamp {
      font-size: 12px;
      color: #5f6368;
    }

    /* 卡片内容 */
    .content-container {
      padding: 16px;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .item-content {
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-word;
      font-size: 14px;
      line-height: 1.5;
      color: #202124;
      overflow: hidden;
      max-height: 150px;
      transition: max-height 0.3s;
      margin-bottom: 8px;
    }

    .item-content.expanded {
      max-height: none;
    }

    /* 展开按钮 */
    .expand-button-container {
      text-align: center;
      margin-top: auto;
      padding: 8px 0;
    }

    .expand-btn {
      background: none;
      border: none;
      color: #1a73e8;
      cursor: pointer;
      font-size: 13px;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .expand-btn:hover {
      background-color: #f1f8ff;
    }

    /* 卡片底部操作栏 */
    .item-actions {
      display: flex;
      justify-content: flex-end;
      padding: 12px 16px;
      border-top: 1px solid #f1f1f1;
      gap: 8px;
    }

    .action-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: background-color 0.2s;
    }

    .copy-btn {
      background-color: #1a73e8;
      color: white;
    }

    .copy-btn:hover {
      background-color: #1765cc;
    }

    .delete-btn {
      background-color: #f8f9fa;
      color: #5f6368;
    }

    .delete-btn:hover {
      background-color: #f1f3f4;
      color: #d93025;
    }

    /* 分页控件 */
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 16px;
      background-color: white;
      border-top: 1px solid #eee;
    }

    .pagination-btn {
      padding: 8px 16px;
      margin: 0 4px;
      background-color: #f8f9fa;
      border: 1px solid #dadce0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      color: #3c4043;
    }

    .pagination-btn:hover:not(:disabled) {
      background-color: #f1f3f4;
    }

    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .pagination-btn.active {
      background-color: #1a73e8;
      color: white;
      border-color: #1a73e8;
    }

    .page-info {
      margin: 0 16px;
      font-size: 14px;
      color: #5f6368;
    }

    /* 空状态 */
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      text-align: center;
      color: #5f6368;
    }

    .empty-state .icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #dadce0;
    }

    .empty-state h2 {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #3c4043;
    }

    .empty-state p {
      font-size: 14px;
      max-width: 400px;
    }

    /* 按钮样式 */
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .btn-primary {
      background-color: #1a73e8;
      color: white;
    }

    .btn-primary:hover {
      background-color: #1765cc;
    }

    .btn-secondary {
      background-color: #f8f9fa;
      color: #3c4043;
      border: 1px solid #dadce0;
    }

    .btn-secondary:hover {
      background-color: #f1f3f4;
    }

    .btn-danger {
      background-color: #d93025;
      color: white;
    }

    .btn-danger:hover {
      background-color: #c5221f;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .history-list {
        grid-template-columns: 1fr;
      }

      .toolbar {
        flex-direction: column;
        align-items: flex-start;
      }

      .filter-container {
        width: 100%;
      }

      .filter-container select {
        flex: 1;
      }
    }

    /* 加载状态 */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
      color: #5f6368;
    }

    .loading-spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1a73e8;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
      margin-right: 12px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 12px 16px;
      background-color: #323232;
      color: white;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      display: flex;
      align-items: center;
      gap: 12px;
      z-index: 1000;
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.3s, transform 0.3s;
    }

    .notification.show {
      opacity: 1;
      transform: translateY(0);
    }

    .notification .message {
      flex: 1;
    }

    .notification .close {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 18px;
      opacity: 0.7;
    }

    .notification .close:hover {
      opacity: 1;
    }
  </style>
</head>
<body>
  <!-- 顶部导航栏 -->
  <header class="header">
    <h1><span class="icon">📋</span> <span id="clipboard-history-title"></span></h1>
    <div class="header-actions">
      <button id="refresh-btn" class="btn btn-secondary"></button>
      <button id="clear-history-btn" class="btn btn-danger"></button>
    </div>
  </header>

  <!-- 主内容区 -->
  <div class="main-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="filter-container">
        <label for="items-per-page" id="items-per-page-label"></label>
        <select id="items-per-page">
          <option value="5">5</option>
          <option value="10">10</option>
          <option value="20">20</option>
          <option value="50">50</option>
        </select>
      </div>
    </div>

    <!-- 历史记录列表容器 -->
    <div class="history-list-container">
      <!-- 历史记录列表 -->
      <div id="history-list" class="history-list">
        <!-- 历史记录项将通过JavaScript动态添加 -->
      </div>

      <!-- 空状态 -->
      <div id="empty-state" class="empty-state" style="display: none;">
        <div class="icon">📋</div>
        <h2 id="empty-state-title"></h2>
        <p id="empty-state-desc"></p>
      </div>

      <!-- 加载状态 -->
      <div id="loading" class="loading" style="display: none;">
        <div class="loading-spinner"></div>
        <span id="loading-text"></span>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination">
      <button id="prev-page" class="pagination-btn" disabled></button>
      <span id="page-info" class="page-info"></span>
      <button id="next-page" class="pagination-btn" disabled></button>
    </div>
  </div>

  <!-- 通知组件 -->
  <div id="notification" class="notification">
    <div id="notification-message" class="message"></div>
    <button id="notification-close" class="close">&times;</button>
  </div>

  <script src="i18n.js"></script>
  <script src="custom-dialog.js"></script>
  <script type="module" src="clipboard-history-ui.js"></script>
</body>
</html>
