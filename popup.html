<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SmartCopy</title>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="shortcuts.css">
  <style>
    body {
      width: 280px;
      padding: 12px;
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      margin: 0;
      background-color: #f8f9fa;
    }

    h1 {
      text-align: center;
      color: #1a73e8;
      font-size: 22px;
      margin-top: 0;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .format-section {
      margin-bottom: 16px;
    }

    .format-label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #202124;
    }

    .format-select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #dadce0;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      color: #202124;
      -webkit-appearance: none;
      appearance: none;
      background-image: url('data:image/svg+xml;utf8,<svg fill="%23202124" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
      background-repeat: no-repeat;
      background-position: right 8px center;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .button-container {
      display: grid;
      grid-template-columns: 1fr;
      gap: 10px;
      margin-bottom: 16px;
    }

    .button {
      padding: 10px 14px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
      background-color: #1a73e8;
      color: white;
    }

    .button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
      background-color: #1765cc;
    }

    .button:active {
      transform: translateY(1px);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* 新的自动复制开关样式 */
    .auto-copy-container {
      display: flex;
      align-items: center;
      background-color: white;
      padding: 12px 15px;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
      margin-bottom: 20px;
    }

    .auto-copy-label {
      flex-grow: 1;
      font-weight: 500;
      color: #202124;
      font-size: 14px;
    }

    /* 新的开关样式 */
    .switch {
      position: relative;
      display: inline-block;
      width: 46px;
      height: 24px;
      flex-shrink: 0;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .3s;
      border-radius: 24px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
    }

    input:checked + .slider {
      background-color: #1a73e8;
    }

    input:focus + .slider {
      box-shadow: 0 0 1px #1a73e8;
    }

    input:checked + .slider:before {
      transform: translateX(22px);
    }

    /* 底部操作区 */
    .footer {
      text-align: center;
      margin-top: 15px;
      padding: 10px 0;
      position: relative;
      z-index: 100;
    }

    .options-btn {
      display: block;
      padding: 12px 24px;
      width: 90%;
      margin: 0 auto;
      background-color: #1a73e8;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all 0.2s;
      position: relative;
      z-index: 10;
    }

    .options-btn:hover {
      background-color: #1765cc;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
      transform: translateY(-1px);
    }

    .options-btn:active {
      transform: translateY(1px);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .clicked {
      animation: button-click 0.2s ease-in-out;
    }

    @keyframes button-click {
      0% { transform: scale(1); }
      50% { transform: scale(0.95); }
      100% { transform: scale(1); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 id="popup-title"></h1>

    <div class="format-section">
      <label for="format-select" class="format-label" id="default-copy-format"></label>
      <select id="format-select" class="format-select">
        <!-- 格式选项将通过JavaScript动态添加 -->
      </select>
    </div>

    <div class="button-container">
      <button id="copy-current-tab" class="button"></button>
      <button id="copy-all-tabs" class="button"></button>
      <button id="clipboard-history" class="button"></button>
    </div>



    <div class="auto-copy-container">
      <span class="auto-copy-label" id="auto-copy-text"></span>
      <label class="switch">
        <input type="checkbox" id="auto-copy-toggle">
        <span class="slider"></span>
      </label>
    </div>

    <div class="auto-copy-container">
      <span class="auto-copy-label" id="super-copy"></span>
      <label class="switch">
        <input type="checkbox" id="super-copy-toggle">
        <span class="slider"></span>
      </label>
    </div>


    <div class="footer">
      <button id="open-options" class="options-btn"></button>
    </div>
  </div>

  <script src="i18n.js"></script>
  <script type="module" src="popup.js"></script>
</body>
</html>
