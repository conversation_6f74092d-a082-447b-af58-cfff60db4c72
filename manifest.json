{"manifest_version": 3, "name": "__MSG_extension_name__", "version": "2.3", "description": "__MSG_extension_description__", "default_locale": "en", "web_accessible_resources": [{"resources": ["_locales/en/messages.json", "_locales/zh_CN/messages.json", "_locales/zh_TW/messages.json"], "matches": ["<all_urls>"]}], "permissions": ["activeTab", "tabs", "clipboardWrite", "contextMenus", "storage", "scripting"], "action": {"default_popup": "popup.html", "default_icon": {"128": "icon.png"}}, "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["logger.js", "content-i18n.js", "turndown.js", "turndown-plugin-gfm.js", "turndown-plugin-enhanced-tables.js", "custom-dialog.js", "content.js"], "css": ["custom-dialog.css"], "run_at": "document_start"}, {"matches": ["<all_urls>"], "js": ["logger.js", "floating-button.js"], "run_at": "document_idle"}], "options_page": "options.html", "icons": {"128": "icon.png"}, "commands": {"_execute_action": {"description": "__MSG_popup_title__"}, "copy-current-tab": {"suggested_key": {"default": "Alt+1"}, "description": "__MSG_copy_current_tab__"}, "copy-all-tabs": {"suggested_key": {"default": "Alt+2"}, "description": "__MSG_copy_all_tabs__"}, "toggle-auto-copy": {"suggested_key": {"default": "Alt+3"}, "description": "__MSG_auto_copy_text__"}, "open-clipboard-history": {"suggested_key": {"default": "Alt+4"}, "description": "__MSG_clipboard_history__"}}}