<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartCopy 浮动按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .feature-list {
            background: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 15px 0;
        }
        .test-instructions {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>🚀 SmartCopy 浮动按钮功能测试</h1>
    
    <div class="feature-list">
        <h2>✨ 新增功能</h2>
        <p><strong>超级复制功能增强：</strong></p>
        <ul>
            <li>用户可在高级设置中自定义是否在页面上显示超级复制的浮动按钮</li>
            <li>默认设置为不显示浮动按钮</li>
            <li>该设置针对所有网站有效</li>
        </ul>
    </div>

    <div class="test-instructions">
        <h2>🧪 测试步骤</h2>
        <ol>
            <li>右键点击SmartCopy扩展图标，选择"选项"打开设置页面</li>
            <li>在设置页面中找到新增的"高级设置"部分</li>
            <li>查看"在页面上显示浮动按钮"开关，默认应该是关闭状态</li>
            <li>开启该开关，保存设置</li>
            <li>刷新此测试页面，应该能看到右下角出现超级复制浮动按钮</li>
            <li>关闭该开关，保存设置</li>
            <li>刷新此测试页面，浮动按钮应该消失</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>📝 测试内容</h2>
        <p>这是一段可以用来测试复制功能的文本。您可以尝试选择这段文字来测试自动复制功能。</p>
        
        <p>当浮动按钮显示时，您可以：</p>
        <ul>
            <li>点击浮动按钮来切换超级复制功能的开启/关闭状态</li>
            <li>拖动浮动按钮来调整其位置</li>
            <li>观察按钮颜色的变化（蓝色=启用，灰色=禁用）</li>
        </ul>
        
        <p>这里有一些受保护的文本内容，您可以测试超级复制功能是否能够绕过复制限制。</p>
    </div>

    <div class="success">
        <h2>✅ 预期结果</h2>
        <ul>
            <li>设置页面应该显示新的"高级设置"部分</li>
            <li>浮动按钮显示开关默认为关闭状态</li>
            <li>开启开关后，所有网页都会显示浮动按钮</li>
            <li>关闭开关后，所有网页的浮动按钮都会消失</li>
            <li>设置变更应该立即生效，无需重启扩展</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 技术实现细节</h2>
        <p>本次功能增强涉及以下文件的修改：</p>
        <ul>
            <li><code>options.js</code> - 添加了新的设置项和处理逻辑</li>
            <li><code>options.html</code> - 添加了高级设置UI</li>
            <li><code>floating-button.js</code> - 修改了默认值为false</li>
            <li><code>i18n.js</code> - 添加了国际化支持</li>
            <li><code>_locales/*/messages.json</code> - 添加了多语言文本</li>
        </ul>
    </div>

    <script>
        // 添加一些测试用的JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SmartCopy 浮动按钮功能测试页面已加载');
            
            // 检查是否有浮动按钮
            setTimeout(() => {
                const floatingButton = document.getElementById('smartcopy-floating-btn');
                if (floatingButton) {
                    console.log('✅ 检测到浮动按钮');
                } else {
                    console.log('❌ 未检测到浮动按钮（这可能是正常的，如果设置为不显示）');
                }
            }, 2000);
        });
        
        // 禁用右键菜单来测试超级复制功能
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            console.log('右键菜单被禁用 - 测试超级复制功能');
        });
        
        // 禁用文本选择来测试超级复制功能
        document.addEventListener('selectstart', function(e) {
            if (Math.random() > 0.5) { // 随机禁用一些选择操作
                e.preventDefault();
                console.log('文本选择被禁用 - 测试超级复制功能');
            }
        });
    </script>
</body>
</html>
