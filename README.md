# SmartCopy 一键全能复制工具

## 简介

SmartCopy 是一款功能强大的 Chrome 扩展，专为提升您的浏览体验而设计。它能帮助您轻松复制标签页的标题和 URL，并提供自动复制选中文本的功能。此外，它还包含一个完整的剪贴板历史记录系统，让您随时查看和重用之前复制的内容。

## 主要功能

### 1. 多语言支持

- **自动语言检测**：根据浏览器语言自动切换界面语言
- **多语言界面**：支持简体中文、繁体中文和英文界面
- **手动切换**：可在设置中手动选择偏好的界面语言
- **全面国际化**：所有界面元素、提示信息和通知均支持多语言显示

### 2. 标签页复制

- **复制当前标签页**：一键复制当前浏览的标签页信息
- **复制所有标签页**：一次性复制当前窗口的所有标签页信息
- **多种格式支持**：支持多种复制格式，包括：
  - 标题 - URL
  - 仅标题
  - 仅 URL
  - Markdown 格式
  - HTML 链接
  - [标题] URL
  - CSV 格式
  - JSON 格式
  - HTML 表格

### 3. 自动复制功能

- **选中文字自动复制**：启用后，只需选中网页上的文字即可自动复制到剪贴板
- **最小字符限制**：可设置自动复制的最小字符数，避免意外复制
- **复制格式选择**：支持复制为纯文本或Markdown格式，满足不同场景需求

### 4. 剪贴板历史记录

- **历史记录管理**：保存您复制过的所有内容，方便随时查看和重用
- **分类显示**：区分手动复制和自动复制的内容
- **一键重复复制**：点击历史记录中的项目即可再次复制
- **清空历史**：一键清空所有历史记录

### 5. 超级复制功能（Super Copy）

#####  功能简介

- **超级复制** 可自动破解网页的复制、右键、选择、粘贴等限制，让你在大多数受限页面也能自由复制内容。

##### 开关与默认行为

- **全局默认关闭**：扩展安装后，超级复制功能默认是关闭的。
- **一键切换**：可通过“启用超级复制功能”开关随时开启/关闭该功能。

##### 记忆机制

- **全局记忆**：切换超级复制开关后，扩展会自动记住你的全局设置，刷新页面或重启浏览器后依然生效。
- **站点个性化**：支持为特定网站单独开启或关闭超级复制，优先级高于全局设置。

#### 使用场景：

1. 支持复制百度文库等优质文件
2. 支持复制各类社交网站
2. 支持复制各类科技网站
3. 支持复制微信公众号等自媒体网站优质内容
4. 支持复制各类文学、小说站、新浪读书、网易阅读等

可以复制的常用中文网站：

1. 百度文库
2. 微信公众号
3. CSDN
4. 小红书
5. 七猫中文网、起点中文网、起点文学、起点女生网
6. 创始中文网
7. 来看中文网 锦文小说网 墨墨言情网 逸云书院
8. 磨铁中文网、磨铁文学、锦文小说网、墨墨言情网、逸云书院
9. 纵横中文网、纵横文学
10. 网易云阅读
11. 豆瓣阅读、豆瓣读书
12. Story Bird
13. 刺猬猫
14. 链家
15. 豆丁网
16. 新浪读书
17. 更多...

### 6. 快捷键支持

- **Alt+1**：复制当前标签页
- **Alt+2**：复制所有标签页
- **Alt+3**：切换自动复制功能
- **Alt+4**：打开剪贴板历史记录

## 使用指南

### 安装扩展

1. 在 Chrome 浏览器中打开扩展管理页面 (chrome://extensions/)
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 SmartCopy 文件夹

### 基本使用

#### 复制标签页

1. 点击扩展图标打开弹出窗口
2. 在"默认复制格式"下拉菜单中选择您喜欢的格式
3. 点击"复制当前标签页"或"复制所有标签页"按钮
4. 内容会自动复制到剪贴板，并显示成功通知

#### 使用自动复制功能

1. 在扩展弹出窗口中，切换"选中文字自动复制"开关
2. 选择您偏好的复制格式：
   - **纯文本**：复制原始文本内容，不保留格式
   - **Markdown**：将选中内容转换为 Markdown 格式，保留基本格式和链接
3. 在网页上选择文本时，文本会自动以选定的格式复制到剪贴板
4. 系统会显示通知，确认文本已复制

#### 选中文字自动复制功能增强

##### 功能简介

SmartCopy 现在提供了增强版的选中文字自动复制功能，让您在浏览网页时能够更智能、更灵活地复制内容：

- **多格式支持**：可选择以纯文本或 Markdown 格式复制内容
- **智能格式保留**：Markdown 模式下会尽可能保留原文的基本格式

##### 使用方法

1. **开启功能**：在扩展弹出窗口中启用 "选中文字自动复制"
2. **选择格式**：在高级设置-自动复制设置-选中文字复制格式-下拉菜单中选择 "纯文本" 或 "Markdown" 格式
3. **复制内容**：在网页上选中任意文本，系统会自动以选定的格式复制到剪贴板
4. **查看结果**：粘贴到任意编辑器中查看复制的内容

##### Markdown 格式特性

当选择 Markdown 格式时，SmartCopy 会：

- 保留网页的基本结构和格式
- 将 HTML 自动转换为 Markdown 格式
- 自动处理常见的文网页格式，如：标题、列表、引用、代码块、链接、图片、无序列表、有序列表、分割线、表格等

##### 适用场景

- **内容创作**：从网页收集素材时保留格式和链接，提高工作效率
- **信息整理**：将网页内容整理到 Markdown 笔记中，保持结构清晰
- **文档编写**：快速从网页提取内容到 Markdown 文档，减少格式调整工作

#### 查看剪贴板历史记录

1. 点击扩展弹出窗口中的"剪贴板历史记录"按钮
2. 在历史记录页面中，您可以：
   - 查看所有复制过的内容
   - 点击项目右侧的复制按钮再次复制内容
   - 点击删除按钮移除单个项目
   - 点击"清空历史"按钮删除所有历史记录

#### 使用超级复制

1. 打开扩展设置页，找到“超级复制设置”区域。
2. 通过开关按钮启用或关闭超级复制。
3. 若需对某个特定网站单独设置，切换该网站下的超级复制即可。
4. 设置后会自动记忆，无需手动保存。

### 使用快捷键

- **Alt+1**：复制当前标签页（使用默认格式）
- **Alt+2**：复制所有标签页（使用默认格式）
- **Alt+3**：启用/禁用自动复制功能
- **Alt+4**：打开剪贴板历史记录页面

## 高级设置

点击扩展弹出窗口底部的"高级设置"链接，可以访问更多设置选项：

- **最小字符数**：设置自动复制功能的最小字符数限制
- **历史记录限制**：设置剪贴板历史记录保存的最大数量
- **通知显示时间**：调整通知显示的持续时间

## 最近更新

### 2025 年 5 月更新

#### 1. 增加多语言支持

- 新增英文、简体中文、繁体中文三种界面语言
- 支持自动检测浏览器语言并切换界面
- 支持在设置中手动选择偏好语言
- 所有界面元素、提示信息和通知均支持多语言显示

#### 2. HTML 转 Markdown 功能优化

- 增强 HTML 转 Markdown 的稳定性和兼容性
- 改进对复杂HTML结构的处理能力
- 修复在某些网站上 HTML 转 Markdown 失效的问题
- 优化 Markdown 格式输出的质量

#### 3. 复制提示优化

- 改进复制成功通知的显示逻辑
- 修复 "复制所有标签页" 时提示消息中变量未正确替换的问题
- 优化通知消息的国际化处理

#### 4. 超级复制功能增强

- 提升超级复制在更多受限网站上的兼容性
- 优化超级复制浮动按钮的拖动体验
- 改进站点特定设置的记忆机制

## 常见问题解答

### Q: 如何切换界面语言？

A: 您可以通过以下步骤切换界面语言：

1. 点击扩展图标打开弹出窗口
2. 点击底部的"高级设置"链接
3. 在"语言设置"部分，选择您偏好的语言：
   - **自动（跟随浏览器）**：根据浏览器语言自动选择
   - **简体中文**：强制使用简体中文界面
   - **繁体中文**：强制使用繁体中文界面
   - **英文**：强制使用英文界面
4. 设置会自动保存并立即生效

### Q: 为什么我选中文字后没有自动复制？

A: 请确认以下几点：

- 自动复制功能已启用（在扩展弹出窗口中查看开关状态）
- 选中的文字长度超过最小字符数限制（默认为 5 个字符）
- 您不是在可编辑区域（如输入框）中选择文字

### Q: 如何切换复制为 Markdown 格式？

A: 在扩展弹出窗口中，启用 "选中文字自动复制" 功能后，您可以在高级设置-自动复制设置-选中文字复制格式-下拉菜单中选择 "Markdown" 格式，之后选中的内容将自动以 Markdown 格式复制到剪贴板。

### Q: Markdown 格式复制会保留哪些格式？

A: Markdown 格式复制会尽可能保留以下格式：

- 文本的基本结构（段落、换行、分割线）
- 链接（转换为 Markdown 链接格式）
- 基本格式，如：标题、无序列表、有序列表、引用等
- 简单的文本样式（如：粗体、斜体）
- 代码块（转换为 Markdown 代码块格式）
- 图片（转换为 Markdown 图片格式）
- 表格（转换为 Markdown 表格格式）

### Q: 为什么在某些网站上HTML转Markdown功能不工作？

A: HTML转Markdown功能依赖于网页的HTML结构。在最新版本中，我们已经大幅提升了兼容性，但仍可能在某些特殊网站上遇到问题。如果您发现某个网站上转换效果不理想，可以尝试以下方法：

1. 确保您使用的是最新版本的SmartCopy扩展
2. 尝试选择更小范围的文本内容进行复制
3. 对于复杂的网页结构，可能需要分段选择并复制
4. 如果问题持续存在，请通过反馈渠道向我们报告，我们会持续优化此功能

### Q: 如何彻底清空剪贴板历史记录？

A: 在剪贴板历史记录页面中，点击"清空历史"按钮，然后在确认对话框中点击"确定"。这将永久删除所有历史记录。

### Q: 我的历史记录会同步到其他设备吗？

A: 不会。剪贴板历史记录仅保存在当前设备上，不会同步到您的其他设备。

## 隐私说明

SmartCopy 尊重您的隐私：

- 所有数据仅存储在您的本地设备上
- 不会将任何数据发送到外部服务器
- 不会收集任何个人信息或浏览历史
- 仅请求必要的权限来实现其功能

## 权限说明

SmartCopy 请求以下权限，每项权限都有明确的用途：

### 标签页访问权限 (`tabs`)
- **用途**：读取当前和所有标签页的标题和 URL，以便复制这些信息
- **说明**：此权限允许扩展获取标签页信息，但不会读取或记录您访问的网页内容

### 存储权限 (`storage`)
- **用途**：保存您的设置和剪贴板历史记录
- **说明**：所有数据仅存储在您的本地设备上，不会上传到任何服务器

### 剪贴板访问权限 (`clipboardWrite`, `clipboardRead`)
- **用途**：将内容复制到剪贴板，以及检测剪贴板变化
- **说明**：此权限用于核心复制功能和自动复制功能

### 上下文菜单权限 (`contextMenus`)
- **用途**：创建右键菜单选项，方便快速复制标签页
- **说明**：提供更便捷的操作方式，无需打开扩展弹出窗口

### 内容脚本权限 (`activeTab`)
- **用途**：在当前页面上运行脚本，以实现自动复制选中文本功能
- **说明**：扩展只会在您选中文本时执行相关操作，不会读取或分析页面内容

### 通知权限 (`notifications`)
- **用途**：显示复制成功或失败的通知
- **说明**：提供操作反馈，让您知道复制是否成功

### Scripting 权限
- **说明**：需要监听复制事件并修改剪贴板内容，Scripting 权限允许扩展注册这些事件监听器。

### 主机权限
- **说明**：为了获取标签页的完整信息（包括标题、URL等），扩展需要能够访问这些标签页所在的网站

### 国际化支持 (`i18n`)
- **用途**：支持多语言界面和提示信息
- **说明**：扩展使用 Chrome 的国际化API来提供多语言支持，根据浏览器语言或用户设置显示相应语言的界面

## 反馈与支持

如果您有任何问题、建议或反馈，请通过以下方式联系我们：

- 在 GitHub 上提交 Issue
- 发送邮件至 <EMAIL>

感谢您使用 SmartCopy！